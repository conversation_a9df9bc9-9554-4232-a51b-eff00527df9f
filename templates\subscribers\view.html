{% extends "base.html" %}

{% block title %}عرض المشترك - {{ subscriber.name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">عرض المشترك</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('subscribers.edit', id=subscriber.id) }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-edit"></i> تعديل
            </a>
            <a href="{{ url_for('subscribers.index') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Subscriber Information -->
<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">معلومات المشترك</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الصندوق:</strong></td>
                                <td>{{ subscriber.box_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>اسم المشترك:</strong></td>
                                <td>{{ subscriber.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهاتف:</strong></td>
                                <td>
                                    {{ subscriber.phone }}
                                    <a href="tel:{{ subscriber.phone }}" class="btn btn-sm btn-outline-primary ms-2">
                                        <i class="fas fa-phone"></i>
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td>
                                    {% if subscriber.email %}
                                        {{ subscriber.email }}
                                        <a href="mailto:{{ subscriber.email }}" class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="fas fa-envelope"></i>
                                        </a>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الهوية/الجواز:</strong></td>
                                <td>{{ subscriber.passport_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>حجم الصندوق:</strong></td>
                                <td>
                                    <span class="badge bg-info">{{ subscriber.box_size }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>العنوان:</strong></td>
                                <td>
                                    {% if subscriber.address %}
                                        {{ subscriber.address }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>حالة الاشتراك:</strong></td>
                                <td>
                                    {% set status = subscriber.subscription_status %}
                                    {% if status == 'Active' %}
                                        <span class="badge bg-success">{{ status | status_arabic }}</span>
                                    {% elif status == 'Expiring Soon' %}
                                        <span class="badge bg-warning">{{ status | status_arabic }}</span>
                                    {% elif status == 'Expired' %}
                                        <span class="badge bg-danger">{{ status | status_arabic }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ status | status_arabic }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="callSubscriber('{{ subscriber.phone }}')">
                        <i class="fas fa-phone"></i> اتصال
                    </button>
                    {% if subscriber.email %}
                    <button class="btn btn-info" onclick="emailSubscriber('{{ subscriber.email }}')">
                        <i class="fas fa-envelope"></i> إرسال بريد إلكتروني
                    </button>
                    {% endif %}
                    <button class="btn btn-success" onclick="renewSubscription({{ subscriber.id }})">
                        <i class="fas fa-sync"></i> تجديد الاشتراك
                    </button>
                    <button class="btn btn-warning" onclick="sendNotification('{{ subscriber.box_number }}')">
                        <i class="fas fa-bell"></i> إرسال إشعار
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Current Subscription Info -->
        {% if subscriber.current_subscription %}
        {% set current_sub = subscriber.current_subscription %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">الاشتراك الحالي</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>تاريخ البداية:</strong></td>
                        <td>{{ current_sub.start_date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الانتهاء:</strong></td>
                        <td>{{ current_sub.end_date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td><strong>المبلغ المدفوع:</strong></td>
                        <td>{{ current_sub.amount_paid | currency }}</td>
                    </tr>
                    <tr>
                        <td><strong>الأيام المتبقية:</strong></td>
                        <td>
                            {% if current_sub.days_remaining > 0 %}
                                <span class="badge bg-{{ 'danger' if current_sub.days_remaining <= 7 else 'warning' if current_sub.days_remaining <= 15 else 'success' }}">
                                    {{ current_sub.days_remaining }} يوم
                                </span>
                            {% else %}
                                <span class="badge bg-danger">منتهي</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Subscription History -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تاريخ الاشتراكات</h5>
        <button class="btn btn-sm btn-primary" onclick="addNewSubscription({{ subscriber.id }})">
            <i class="fas fa-plus"></i> إضافة اشتراك جديد
        </button>
    </div>
    <div class="card-body">
        {% if subscriptions %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>تاريخ البداية</th>
                            <th>تاريخ الانتهاء</th>
                            <th>المدة</th>
                            <th>المبلغ المدفوع</th>
                            <th>تاريخ الدفع</th>
                            <th>الحالة</th>
                            <th>ملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for subscription in subscriptions %}
                        <tr class="{{ 'table-success' if subscription.is_active else '' }}">
                            <td>{{ subscription.start_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ subscription.end_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ (subscription.end_date - subscription.start_date).days }} يوم</td>
                            <td>{{ subscription.amount_paid | currency }}</td>
                            <td>
                                {% if subscription.payment_date %}
                                    {{ subscription.payment_date.strftime('%Y-%m-%d') }}
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if subscription.is_active %}
                                    {% if subscription.is_expired %}
                                        <span class="badge bg-danger">منتهي</span>
                                    {% elif subscription.is_expiring_soon %}
                                        <span class="badge bg-warning">ينتهي قريباً</span>
                                    {% else %}
                                        <span class="badge bg-success">نشط</span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if subscription.notes %}
                                    {{ subscription.notes[:50] }}{% if subscription.notes|length > 50 %}...{% endif %}
                                {% else %}
                                    <span class="text-muted">لا توجد ملاحظات</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="editSubscription({{ subscription.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% if not subscription.is_active %}
                                    <button class="btn btn-outline-danger" onclick="deleteSubscription({{ subscription.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد اشتراكات</h5>
                <p class="text-muted">لم يتم إنشاء أي اشتراكات لهذا المشترك بعد.</p>
                <button class="btn btn-primary" onclick="addNewSubscription({{ subscriber.id }})">
                    <i class="fas fa-plus"></i> إضافة اشتراك جديد
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Add Subscription Modal -->
<div class="modal fade" id="addSubscriptionModal" tabindex="-1" aria-labelledby="addSubscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addSubscriptionModalLabel">إضافة اشتراك جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addSubscriptionForm">
                    <div class="mb-3">
                        <label for="start_date" class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="start_date" name="start_date" required>
                    </div>
                    <div class="mb-3">
                        <label for="end_date" class="form-label">تاريخ الانتهاء <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="end_date" name="end_date" required>
                    </div>
                    <div class="mb-3">
                        <label for="amount_paid" class="form-label">المبلغ المدفوع <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="amount_paid" name="amount_paid" step="0.001" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment_date" class="form-label">تاريخ الدفع</label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitAddSubscription()">إضافة الاشتراك</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function callSubscriber(phone) {
    window.open(`tel:${phone}`);
}

function emailSubscriber(email) {
    window.open(`mailto:${email}`);
}

function sendNotification(boxNumber) {
    // This will be implemented with the notification system
    alert(`سيتم إرسال إشعار للصندوق رقم: ${boxNumber}`);
}

function renewSubscription(subscriberId) {
    // Show modal to add new subscription
    showAddSubscriptionModal(subscriberId);
}

function addNewSubscription(subscriberId) {
    // Show modal to add new subscription
    showAddSubscriptionModal(subscriberId);
}

function editSubscription(subscriptionId) {
    // Show modal to edit subscription
    showEditSubscriptionModal(subscriptionId);
}

function deleteSubscription(subscriptionId) {
    if (confirm('هل أنت متأكد من حذف هذا الاشتراك؟')) {
        fetch(`/subscribers/subscription/${subscriptionId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Delete error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
}

function showAddSubscriptionModal(subscriberId) {
    // Set default dates
    const today = new Date().toISOString().split('T')[0];
    const nextYear = new Date();
    nextYear.setFullYear(nextYear.getFullYear() + 1);

    document.getElementById('start_date').value = today;
    document.getElementById('end_date').value = nextYear.toISOString().split('T')[0];
    document.getElementById('payment_date').value = today;

    // Set default amount based on box size
    const boxSizeAmounts = {'Small': 500.000, 'Medium': 750.000, 'Large': 1000.000};
    const boxSize = '{{ subscriber.box_size }}';
    document.getElementById('amount_paid').value = boxSizeAmounts[boxSize] || 500.000;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('addSubscriptionModal'));
    modal.show();
}

function submitAddSubscription() {
    const form = document.getElementById('addSubscriptionForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    fetch(`/subscribers/{{ subscriber.id }}/add-subscription`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal and reload page
            const modal = bootstrap.Modal.getInstance(document.getElementById('addSubscriptionModal'));
            modal.hide();
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Add subscription error:', error);
        alert('حدث خطأ أثناء إضافة الاشتراك');
    });
}

function showEditSubscriptionModal(subscriptionId) {
    // This would need to be implemented with subscription editing functionality
    alert('سيتم تطوير تعديل الاشتراكات قريباً');
}
</script>
{% endblock %}
