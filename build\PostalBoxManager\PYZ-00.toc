('D:\\\u200f\u200fPostal Box Management\\build\\PostalBoxManager\\PYZ-00.pyz',
 [('PIL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Program Files\\Python311\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Program Files\\Python311\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Program Files\\Python311\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python311\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python311\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Program Files\\Python311\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Program Files\\Python311\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python311\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files\\Python311\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Program Files\\Python311\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python311\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python311\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('alembic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\__init__.py',
   'PYMODULE'),
  ('alembic.autogenerate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\autogenerate\\__init__.py',
   'PYMODULE'),
  ('alembic.autogenerate.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\autogenerate\\api.py',
   'PYMODULE'),
  ('alembic.autogenerate.compare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\autogenerate\\compare.py',
   'PYMODULE'),
  ('alembic.autogenerate.render',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\autogenerate\\render.py',
   'PYMODULE'),
  ('alembic.autogenerate.rewriter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\autogenerate\\rewriter.py',
   'PYMODULE'),
  ('alembic.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\command.py',
   'PYMODULE'),
  ('alembic.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\config.py',
   'PYMODULE'),
  ('alembic.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\context.py',
   'PYMODULE'),
  ('alembic.ddl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\ddl\\__init__.py',
   'PYMODULE'),
  ('alembic.ddl._autogen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\ddl\\_autogen.py',
   'PYMODULE'),
  ('alembic.ddl.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\ddl\\base.py',
   'PYMODULE'),
  ('alembic.ddl.impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\ddl\\impl.py',
   'PYMODULE'),
  ('alembic.ddl.mssql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\ddl\\mssql.py',
   'PYMODULE'),
  ('alembic.ddl.mysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\ddl\\mysql.py',
   'PYMODULE'),
  ('alembic.ddl.oracle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\ddl\\oracle.py',
   'PYMODULE'),
  ('alembic.ddl.postgresql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\ddl\\postgresql.py',
   'PYMODULE'),
  ('alembic.ddl.sqlite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\ddl\\sqlite.py',
   'PYMODULE'),
  ('alembic.op',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\op.py',
   'PYMODULE'),
  ('alembic.operations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\operations\\__init__.py',
   'PYMODULE'),
  ('alembic.operations.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\operations\\base.py',
   'PYMODULE'),
  ('alembic.operations.batch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\operations\\batch.py',
   'PYMODULE'),
  ('alembic.operations.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\operations\\ops.py',
   'PYMODULE'),
  ('alembic.operations.schemaobj',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\operations\\schemaobj.py',
   'PYMODULE'),
  ('alembic.operations.toimpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\operations\\toimpl.py',
   'PYMODULE'),
  ('alembic.runtime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\runtime\\__init__.py',
   'PYMODULE'),
  ('alembic.runtime.environment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\runtime\\environment.py',
   'PYMODULE'),
  ('alembic.runtime.migration',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\runtime\\migration.py',
   'PYMODULE'),
  ('alembic.script',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\script\\__init__.py',
   'PYMODULE'),
  ('alembic.script.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\script\\base.py',
   'PYMODULE'),
  ('alembic.script.revision',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\script\\revision.py',
   'PYMODULE'),
  ('alembic.script.write_hooks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\script\\write_hooks.py',
   'PYMODULE'),
  ('alembic.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\util\\__init__.py',
   'PYMODULE'),
  ('alembic.util.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\util\\compat.py',
   'PYMODULE'),
  ('alembic.util.editor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\util\\editor.py',
   'PYMODULE'),
  ('alembic.util.exc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\util\\exc.py',
   'PYMODULE'),
  ('alembic.util.langhelpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\util\\langhelpers.py',
   'PYMODULE'),
  ('alembic.util.messaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\util\\messaging.py',
   'PYMODULE'),
  ('alembic.util.pyfiles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\util\\pyfiles.py',
   'PYMODULE'),
  ('alembic.util.sqla_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\alembic\\util\\sqla_compat.py',
   'PYMODULE'),
  ('app',
   'D:\\\u200f\u200fPostal Box Management\\app\\__init__.py',
   'PYMODULE'),
  ('app.auth',
   'D:\\\u200f\u200fPostal Box Management\\app\\auth\\__init__.py',
   'PYMODULE'),
  ('app.auth.routes',
   'D:\\\u200f\u200fPostal Box Management\\app\\auth\\routes.py',
   'PYMODULE'),
  ('app.backup',
   'D:\\\u200f\u200fPostal Box Management\\app\\backup.py',
   'PYMODULE'),
  ('app.backup_bp',
   'D:\\\u200f\u200fPostal Box Management\\app\\backup_bp\\__init__.py',
   'PYMODULE'),
  ('app.backup_bp.routes',
   'D:\\\u200f\u200fPostal Box Management\\app\\backup_bp\\routes.py',
   'PYMODULE'),
  ('app.backup_monitor',
   'D:\\\u200f\u200fPostal Box Management\\app\\backup_monitor.py',
   'PYMODULE'),
  ('app.extensions',
   'D:\\\u200f\u200fPostal Box Management\\app\\extensions.py',
   'PYMODULE'),
  ('app.import_export',
   'D:\\\u200f\u200fPostal Box Management\\app\\import_export\\__init__.py',
   'PYMODULE'),
  ('app.import_export.routes',
   'D:\\\u200f\u200fPostal Box Management\\app\\import_export\\routes.py',
   'PYMODULE'),
  ('app.main',
   'D:\\\u200f\u200fPostal Box Management\\app\\main\\__init__.py',
   'PYMODULE'),
  ('app.main.routes',
   'D:\\\u200f\u200fPostal Box Management\\app\\main\\routes.py',
   'PYMODULE'),
  ('app.models',
   'D:\\\u200f\u200fPostal Box Management\\app\\models.py',
   'PYMODULE'),
  ('app.notifications',
   'D:\\\u200f\u200fPostal Box Management\\app\\notifications.py',
   'PYMODULE'),
  ('app.notifications_bp',
   'D:\\\u200f\u200fPostal Box Management\\app\\notifications_bp\\__init__.py',
   'PYMODULE'),
  ('app.notifications_bp.routes',
   'D:\\\u200f\u200fPostal Box Management\\app\\notifications_bp\\routes.py',
   'PYMODULE'),
  ('app.reports',
   'D:\\\u200f\u200fPostal Box Management\\app\\reports\\__init__.py',
   'PYMODULE'),
  ('app.reports.routes',
   'D:\\\u200f\u200fPostal Box Management\\app\\reports\\routes.py',
   'PYMODULE'),
  ('app.scheduler',
   'D:\\\u200f\u200fPostal Box Management\\app\\scheduler.py',
   'PYMODULE'),
  ('app.subscribers',
   'D:\\\u200f\u200fPostal Box Management\\app\\subscribers\\__init__.py',
   'PYMODULE'),
  ('app.subscribers.routes',
   'D:\\\u200f\u200fPostal Box Management\\app\\subscribers\\routes.py',
   'PYMODULE'),
  ('apscheduler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\__init__.py',
   'PYMODULE'),
  ('apscheduler.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\events.py',
   'PYMODULE'),
  ('apscheduler.executors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\executors\\__init__.py',
   'PYMODULE'),
  ('apscheduler.executors.asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\executors\\asyncio.py',
   'PYMODULE'),
  ('apscheduler.executors.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\executors\\base.py',
   'PYMODULE'),
  ('apscheduler.executors.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\executors\\debug.py',
   'PYMODULE'),
  ('apscheduler.executors.gevent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\executors\\gevent.py',
   'PYMODULE'),
  ('apscheduler.executors.pool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\executors\\pool.py',
   'PYMODULE'),
  ('apscheduler.executors.tornado',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\executors\\tornado.py',
   'PYMODULE'),
  ('apscheduler.executors.twisted',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\executors\\twisted.py',
   'PYMODULE'),
  ('apscheduler.job',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\job.py',
   'PYMODULE'),
  ('apscheduler.jobstores',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\jobstores\\__init__.py',
   'PYMODULE'),
  ('apscheduler.jobstores.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\jobstores\\base.py',
   'PYMODULE'),
  ('apscheduler.jobstores.etcd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\jobstores\\etcd.py',
   'PYMODULE'),
  ('apscheduler.jobstores.memory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\jobstores\\memory.py',
   'PYMODULE'),
  ('apscheduler.jobstores.mongodb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\jobstores\\mongodb.py',
   'PYMODULE'),
  ('apscheduler.jobstores.redis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\jobstores\\redis.py',
   'PYMODULE'),
  ('apscheduler.jobstores.rethinkdb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\jobstores\\rethinkdb.py',
   'PYMODULE'),
  ('apscheduler.jobstores.sqlalchemy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\jobstores\\sqlalchemy.py',
   'PYMODULE'),
  ('apscheduler.jobstores.zookeeper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\jobstores\\zookeeper.py',
   'PYMODULE'),
  ('apscheduler.schedulers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\schedulers\\__init__.py',
   'PYMODULE'),
  ('apscheduler.schedulers.asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\schedulers\\asyncio.py',
   'PYMODULE'),
  ('apscheduler.schedulers.background',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\schedulers\\background.py',
   'PYMODULE'),
  ('apscheduler.schedulers.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\schedulers\\base.py',
   'PYMODULE'),
  ('apscheduler.schedulers.blocking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\schedulers\\blocking.py',
   'PYMODULE'),
  ('apscheduler.schedulers.gevent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\schedulers\\gevent.py',
   'PYMODULE'),
  ('apscheduler.schedulers.qt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\schedulers\\qt.py',
   'PYMODULE'),
  ('apscheduler.schedulers.tornado',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\schedulers\\tornado.py',
   'PYMODULE'),
  ('apscheduler.schedulers.twisted',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\schedulers\\twisted.py',
   'PYMODULE'),
  ('apscheduler.triggers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\triggers\\__init__.py',
   'PYMODULE'),
  ('apscheduler.triggers.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\triggers\\base.py',
   'PYMODULE'),
  ('apscheduler.triggers.calendarinterval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\triggers\\calendarinterval.py',
   'PYMODULE'),
  ('apscheduler.triggers.combining',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\triggers\\combining.py',
   'PYMODULE'),
  ('apscheduler.triggers.cron',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\triggers\\cron\\__init__.py',
   'PYMODULE'),
  ('apscheduler.triggers.cron.expressions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\triggers\\cron\\expressions.py',
   'PYMODULE'),
  ('apscheduler.triggers.cron.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\triggers\\cron\\fields.py',
   'PYMODULE'),
  ('apscheduler.triggers.date',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\triggers\\date.py',
   'PYMODULE'),
  ('apscheduler.triggers.interval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\triggers\\interval.py',
   'PYMODULE'),
  ('apscheduler.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\apscheduler\\util.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python311\\Lib\\argparse.py', 'PYMODULE'),
  ('asgiref',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\asgiref\\__init__.py',
   'PYMODULE'),
  ('asgiref.current_thread_executor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\asgiref\\current_thread_executor.py',
   'PYMODULE'),
  ('asgiref.local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\asgiref\\local.py',
   'PYMODULE'),
  ('asgiref.sync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\asgiref\\sync.py',
   'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python311\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python311\\Lib\\base64.py', 'PYMODULE'),
  ('bcrypt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bdb', 'C:\\Program Files\\Python311\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python311\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bs4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4._warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4.filter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python311\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python311\\Lib\\calendar.py', 'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'C:\\Program Files\\Python311\\Lib\\cgi.py', 'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'C:\\Program Files\\Python311\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Program Files\\Python311\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Program Files\\Python311\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Program Files\\Python311\\Lib\\colorsys.py', 'PYMODULE'),
  ('commctrl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program Files\\Python311\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program Files\\Python311\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program Files\\Python311\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program Files\\Python311\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program Files\\Python311\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config', 'D:\\\u200f\u200fPostal Box Management\\config.py', 'PYMODULE'),
  ('configparser',
   'C:\\Program Files\\Python311\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program Files\\Python311\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python311\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python311\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python311\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python311\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Program Files\\Python311\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python311\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Program Files\\Python311\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Program Files\\Python311\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Program Files\\Python311\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Program Files\\Python311\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Program Files\\Python311\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python311\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python311\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python311\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python311\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\Program Files\\Python311\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python311\\Lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'C:\\Program Files\\Python311\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Program Files\\Python311\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Program Files\\Python311\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Program Files\\Python311\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Program Files\\Python311\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Program Files\\Python311\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Program Files\\Python311\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Program Files\\Python311\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Program Files\\Python311\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Program Files\\Python311\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Program Files\\Python311\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Program Files\\Python311\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Program Files\\Python311\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Program Files\\Python311\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Program Files\\Python311\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Program Files\\Python311\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Program Files\\Python311\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Program Files\\Python311\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Program Files\\Python311\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Program Files\\Python311\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Program Files\\Python311\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Program Files\\Python311\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Program Files\\Python311\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Program Files\\Python311\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Program Files\\Python311\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Program Files\\Python311\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'C:\\Program Files\\Python311\\Lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Program Files\\Python311\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Program Files\\Python311\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Program Files\\Python311\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('dns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dns\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dns\\rdtypes\\__init__.py',
   'PYMODULE'),
  ('dns.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dns\\version.py',
   'PYMODULE'),
  ('doctest', 'C:\\Program Files\\Python311\\Lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email',
   'C:\\Program Files\\Python311\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python311\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python311\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python311\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python311\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python311\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python311\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python311\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python311\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python311\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python311\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python311\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python311\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python311\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python311\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python311\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.mime',
   'C:\\Program Files\\Python311\\Lib\\email\\mime\\__init__.py',
   'PYMODULE'),
  ('email.mime.base',
   'C:\\Program Files\\Python311\\Lib\\email\\mime\\base.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Program Files\\Python311\\Lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Program Files\\Python311\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.text',
   'C:\\Program Files\\Python311\\Lib\\email\\mime\\text.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python311\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python311\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python311\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python311\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('flask',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.scaffold',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask_login',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_login\\__init__.py',
   'PYMODULE'),
  ('flask_login.__about__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_login\\__about__.py',
   'PYMODULE'),
  ('flask_login.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_login\\config.py',
   'PYMODULE'),
  ('flask_login.login_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_login\\login_manager.py',
   'PYMODULE'),
  ('flask_login.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_login\\mixins.py',
   'PYMODULE'),
  ('flask_login.signals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_login\\signals.py',
   'PYMODULE'),
  ('flask_login.test_client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_login\\test_client.py',
   'PYMODULE'),
  ('flask_login.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_login\\utils.py',
   'PYMODULE'),
  ('flask_migrate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_migrate\\__init__.py',
   'PYMODULE'),
  ('flask_migrate.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_migrate\\cli.py',
   'PYMODULE'),
  ('flask_sqlalchemy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('flask_sqlalchemy.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_sqlalchemy\\cli.py',
   'PYMODULE'),
  ('flask_sqlalchemy.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_sqlalchemy\\extension.py',
   'PYMODULE'),
  ('flask_sqlalchemy.model',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_sqlalchemy\\model.py',
   'PYMODULE'),
  ('flask_sqlalchemy.pagination',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_sqlalchemy\\pagination.py',
   'PYMODULE'),
  ('flask_sqlalchemy.query',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_sqlalchemy\\query.py',
   'PYMODULE'),
  ('flask_sqlalchemy.record_queries',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_sqlalchemy\\record_queries.py',
   'PYMODULE'),
  ('flask_sqlalchemy.session',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_sqlalchemy\\session.py',
   'PYMODULE'),
  ('flask_sqlalchemy.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_sqlalchemy\\table.py',
   'PYMODULE'),
  ('flask_sqlalchemy.track_modifications',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask_sqlalchemy\\track_modifications.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python311\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python311\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python311\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python311\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python311\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python311\\Lib\\gettext.py', 'PYMODULE'),
  ('gevent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\__init__.py',
   'PYMODULE'),
  ('gevent._abstract_linkable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_abstract_linkable.py',
   'PYMODULE'),
  ('gevent._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_compat.py',
   'PYMODULE'),
  ('gevent._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_config.py',
   'PYMODULE'),
  ('gevent._ffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_ffi\\__init__.py',
   'PYMODULE'),
  ('gevent._ffi.callback',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_ffi\\callback.py',
   'PYMODULE'),
  ('gevent._ffi.loop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_ffi\\loop.py',
   'PYMODULE'),
  ('gevent._ffi.watcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_ffi\\watcher.py',
   'PYMODULE'),
  ('gevent._fileobjectcommon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_fileobjectcommon.py',
   'PYMODULE'),
  ('gevent._fileobjectposix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_fileobjectposix.py',
   'PYMODULE'),
  ('gevent._greenlet_primitives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_greenlet_primitives.py',
   'PYMODULE'),
  ('gevent._hub_local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_hub_local.py',
   'PYMODULE'),
  ('gevent._hub_primitives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_hub_primitives.py',
   'PYMODULE'),
  ('gevent._ident',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_ident.py',
   'PYMODULE'),
  ('gevent._imap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_imap.py',
   'PYMODULE'),
  ('gevent._interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_interfaces.py',
   'PYMODULE'),
  ('gevent._monitor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_monitor.py',
   'PYMODULE'),
  ('gevent._patcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_patcher.py',
   'PYMODULE'),
  ('gevent._semaphore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_semaphore.py',
   'PYMODULE'),
  ('gevent._socket3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_socket3.py',
   'PYMODULE'),
  ('gevent._socketcommon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_socketcommon.py',
   'PYMODULE'),
  ('gevent._tblib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_tblib.py',
   'PYMODULE'),
  ('gevent._threading',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_threading.py',
   'PYMODULE'),
  ('gevent._tracer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_tracer.py',
   'PYMODULE'),
  ('gevent._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_util.py',
   'PYMODULE'),
  ('gevent._waiter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_waiter.py',
   'PYMODULE'),
  ('gevent.ares',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\ares.py',
   'PYMODULE'),
  ('gevent.backdoor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\backdoor.py',
   'PYMODULE'),
  ('gevent.baseserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\baseserver.py',
   'PYMODULE'),
  ('gevent.builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\builtins.py',
   'PYMODULE'),
  ('gevent.contextvars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\contextvars.py',
   'PYMODULE'),
  ('gevent.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\core.py',
   'PYMODULE'),
  ('gevent.event',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\event.py',
   'PYMODULE'),
  ('gevent.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\events.py',
   'PYMODULE'),
  ('gevent.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\exceptions.py',
   'PYMODULE'),
  ('gevent.fileobject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\fileobject.py',
   'PYMODULE'),
  ('gevent.greenlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\greenlet.py',
   'PYMODULE'),
  ('gevent.hub',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\hub.py',
   'PYMODULE'),
  ('gevent.libev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\libev\\__init__.py',
   'PYMODULE'),
  ('gevent.libev._corecffi_build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\libev\\_corecffi_build.py',
   'PYMODULE'),
  ('gevent.libev.corecffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\libev\\corecffi.py',
   'PYMODULE'),
  ('gevent.libev.watcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\libev\\watcher.py',
   'PYMODULE'),
  ('gevent.libuv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\libuv\\__init__.py',
   'PYMODULE'),
  ('gevent.libuv._corecffi_build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\libuv\\_corecffi_build.py',
   'PYMODULE'),
  ('gevent.libuv.loop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\libuv\\loop.py',
   'PYMODULE'),
  ('gevent.libuv.watcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\libuv\\watcher.py',
   'PYMODULE'),
  ('gevent.local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\local.py',
   'PYMODULE'),
  ('gevent.lock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\lock.py',
   'PYMODULE'),
  ('gevent.monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\monkey\\__init__.py',
   'PYMODULE'),
  ('gevent.monkey.__main__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\monkey\\__main__.py',
   'PYMODULE'),
  ('gevent.monkey._errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\monkey\\_errors.py',
   'PYMODULE'),
  ('gevent.monkey._main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\monkey\\_main.py',
   'PYMODULE'),
  ('gevent.monkey._patch_thread_common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\monkey\\_patch_thread_common.py',
   'PYMODULE'),
  ('gevent.monkey._patch_thread_gte313',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\monkey\\_patch_thread_gte313.py',
   'PYMODULE'),
  ('gevent.monkey._patch_thread_lt313',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\monkey\\_patch_thread_lt313.py',
   'PYMODULE'),
  ('gevent.monkey._state',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\monkey\\_state.py',
   'PYMODULE'),
  ('gevent.monkey._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\monkey\\_util.py',
   'PYMODULE'),
  ('gevent.monkey.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\monkey\\api.py',
   'PYMODULE'),
  ('gevent.os',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\os.py',
   'PYMODULE'),
  ('gevent.pool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\pool.py',
   'PYMODULE'),
  ('gevent.pywsgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\pywsgi.py',
   'PYMODULE'),
  ('gevent.queue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\queue.py',
   'PYMODULE'),
  ('gevent.resolver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\resolver\\__init__.py',
   'PYMODULE'),
  ('gevent.resolver._addresses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\resolver\\_addresses.py',
   'PYMODULE'),
  ('gevent.resolver._hostsfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\resolver\\_hostsfile.py',
   'PYMODULE'),
  ('gevent.resolver.ares',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\resolver\\ares.py',
   'PYMODULE'),
  ('gevent.resolver.blocking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\resolver\\blocking.py',
   'PYMODULE'),
  ('gevent.resolver.dnspython',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\resolver\\dnspython.py',
   'PYMODULE'),
  ('gevent.resolver.thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\resolver\\thread.py',
   'PYMODULE'),
  ('gevent.resolver_ares',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\resolver_ares.py',
   'PYMODULE'),
  ('gevent.resolver_thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\resolver_thread.py',
   'PYMODULE'),
  ('gevent.select',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\select.py',
   'PYMODULE'),
  ('gevent.selectors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\selectors.py',
   'PYMODULE'),
  ('gevent.server',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\server.py',
   'PYMODULE'),
  ('gevent.signal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\signal.py',
   'PYMODULE'),
  ('gevent.socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\socket.py',
   'PYMODULE'),
  ('gevent.ssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\ssl.py',
   'PYMODULE'),
  ('gevent.subprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\subprocess.py',
   'PYMODULE'),
  ('gevent.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\__init__.py',
   'PYMODULE'),
  ('gevent.testing.errorhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\errorhandler.py',
   'PYMODULE'),
  ('gevent.testing.exception',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\exception.py',
   'PYMODULE'),
  ('gevent.testing.flaky',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\flaky.py',
   'PYMODULE'),
  ('gevent.testing.hub',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\hub.py',
   'PYMODULE'),
  ('gevent.testing.leakcheck',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\leakcheck.py',
   'PYMODULE'),
  ('gevent.testing.modules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\modules.py',
   'PYMODULE'),
  ('gevent.testing.monkey_test',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\monkey_test.py',
   'PYMODULE'),
  ('gevent.testing.openfiles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\openfiles.py',
   'PYMODULE'),
  ('gevent.testing.params',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\params.py',
   'PYMODULE'),
  ('gevent.testing.patched_tests_setup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\patched_tests_setup.py',
   'PYMODULE'),
  ('gevent.testing.resources',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\resources.py',
   'PYMODULE'),
  ('gevent.testing.six',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\six.py',
   'PYMODULE'),
  ('gevent.testing.skipping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\skipping.py',
   'PYMODULE'),
  ('gevent.testing.sockets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\sockets.py',
   'PYMODULE'),
  ('gevent.testing.support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\support.py',
   'PYMODULE'),
  ('gevent.testing.switching',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\switching.py',
   'PYMODULE'),
  ('gevent.testing.sysinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\sysinfo.py',
   'PYMODULE'),
  ('gevent.testing.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\testcase.py',
   'PYMODULE'),
  ('gevent.testing.testrunner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\testrunner.py',
   'PYMODULE'),
  ('gevent.testing.timing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\timing.py',
   'PYMODULE'),
  ('gevent.testing.travis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\travis.py',
   'PYMODULE'),
  ('gevent.testing.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\testing\\util.py',
   'PYMODULE'),
  ('gevent.tests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\__init__.py',
   'PYMODULE'),
  ('gevent.tests.__main__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\__main__.py',
   'PYMODULE'),
  ('gevent.tests._blocks_at_top_level',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\_blocks_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests._import_import_patch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\_import_import_patch.py',
   'PYMODULE'),
  ('gevent.tests._import_patch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\_import_patch.py',
   'PYMODULE'),
  ('gevent.tests._import_wait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\_import_wait.py',
   'PYMODULE'),
  ('gevent.tests._imports_at_top_level',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\_imports_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests._imports_imports_at_top_level',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\_imports_imports_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests.getaddrinfo_module',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\getaddrinfo_module.py',
   'PYMODULE'),
  ('gevent.tests.known_failures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\known_failures.py',
   'PYMODULE'),
  ('gevent.tests.lock_tests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\lock_tests.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\monkey_package\\__init__.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.__main__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\monkey_package\\__main__.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue1526_no_monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\monkey_package\\issue1526_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue1526_with_monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\monkey_package\\issue1526_with_monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue302monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\monkey_package\\issue302monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.script',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\monkey_package\\script.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.threadpool_monkey_patches',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\monkey_package\\threadpool_monkey_patches.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.threadpool_no_monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\monkey_package\\threadpool_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__GreenletExit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__GreenletExit.py',
   'PYMODULE'),
  ('gevent.tests.test___config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test___config.py',
   'PYMODULE'),
  ('gevent.tests.test___ident',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test___ident.py',
   'PYMODULE'),
  ('gevent.tests.test___monitor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test___monitor.py',
   'PYMODULE'),
  ('gevent.tests.test___monkey_patching',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test___monkey_patching.py',
   'PYMODULE'),
  ('gevent.tests.test__all__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__all__.py',
   'PYMODULE'),
  ('gevent.tests.test__api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__api.py',
   'PYMODULE'),
  ('gevent.tests.test__api_timeout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__api_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__ares_host_result',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__ares_host_result.py',
   'PYMODULE'),
  ('gevent.tests.test__ares_timeout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__ares_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__backdoor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__backdoor.py',
   'PYMODULE'),
  ('gevent.tests.test__close_backend_fd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__close_backend_fd.py',
   'PYMODULE'),
  ('gevent.tests.test__compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__compat.py',
   'PYMODULE'),
  ('gevent.tests.test__contextvars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__contextvars.py',
   'PYMODULE'),
  ('gevent.tests.test__core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__core.py',
   'PYMODULE'),
  ('gevent.tests.test__core_async',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__core_async.py',
   'PYMODULE'),
  ('gevent.tests.test__core_callback',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__core_callback.py',
   'PYMODULE'),
  ('gevent.tests.test__core_fork',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__core_fork.py',
   'PYMODULE'),
  ('gevent.tests.test__core_loop_run',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__core_loop_run.py',
   'PYMODULE'),
  ('gevent.tests.test__core_stat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__core_stat.py',
   'PYMODULE'),
  ('gevent.tests.test__core_timer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__core_timer.py',
   'PYMODULE'),
  ('gevent.tests.test__core_watcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__core_watcher.py',
   'PYMODULE'),
  ('gevent.tests.test__destroy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__destroy.py',
   'PYMODULE'),
  ('gevent.tests.test__destroy_default_loop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__destroy_default_loop.py',
   'PYMODULE'),
  ('gevent.tests.test__doctests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__doctests.py',
   'PYMODULE'),
  ('gevent.tests.test__environ',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__environ.py',
   'PYMODULE'),
  ('gevent.tests.test__event',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__event.py',
   'PYMODULE'),
  ('gevent.tests.test__events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__events.py',
   'PYMODULE'),
  ('gevent.tests.test__example_echoserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__example_echoserver.py',
   'PYMODULE'),
  ('gevent.tests.test__example_portforwarder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__example_portforwarder.py',
   'PYMODULE'),
  ('gevent.tests.test__example_udp_client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__example_udp_client.py',
   'PYMODULE'),
  ('gevent.tests.test__example_udp_server',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__example_udp_server.py',
   'PYMODULE'),
  ('gevent.tests.test__example_webproxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__example_webproxy.py',
   'PYMODULE'),
  ('gevent.tests.test__example_wsgiserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__example_wsgiserver.py',
   'PYMODULE'),
  ('gevent.tests.test__example_wsgiserver_ssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__example_wsgiserver_ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__examples',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__examples.py',
   'PYMODULE'),
  ('gevent.tests.test__exc_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__exc_info.py',
   'PYMODULE'),
  ('gevent.tests.test__execmodules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__execmodules.py',
   'PYMODULE'),
  ('gevent.tests.test__fileobject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__fileobject.py',
   'PYMODULE'),
  ('gevent.tests.test__getaddrinfo_import',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__getaddrinfo_import.py',
   'PYMODULE'),
  ('gevent.tests.test__greenio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__greenio.py',
   'PYMODULE'),
  ('gevent.tests.test__greenlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__greenletset',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__greenletset.py',
   'PYMODULE'),
  ('gevent.tests.test__greenness',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__greenness.py',
   'PYMODULE'),
  ('gevent.tests.test__hub',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__hub.py',
   'PYMODULE'),
  ('gevent.tests.test__hub_join',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__hub_join.py',
   'PYMODULE'),
  ('gevent.tests.test__hub_join_timeout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__hub_join_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__import_blocking_in_greenlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__import_blocking_in_greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__import_wait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__import_wait.py',
   'PYMODULE'),
  ('gevent.tests.test__issue112',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__issue112.py',
   'PYMODULE'),
  ('gevent.tests.test__issue1686',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__issue1686.py',
   'PYMODULE'),
  ('gevent.tests.test__issue1864',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__issue1864.py',
   'PYMODULE'),
  ('gevent.tests.test__issue230',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__issue230.py',
   'PYMODULE'),
  ('gevent.tests.test__issue330',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__issue330.py',
   'PYMODULE'),
  ('gevent.tests.test__issue467',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__issue467.py',
   'PYMODULE'),
  ('gevent.tests.test__issue6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__issue6.py',
   'PYMODULE'),
  ('gevent.tests.test__issue600',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__issue600.py',
   'PYMODULE'),
  ('gevent.tests.test__issue607',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__issue607.py',
   'PYMODULE'),
  ('gevent.tests.test__issue639',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__issue639.py',
   'PYMODULE'),
  ('gevent.tests.test__issue_728',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__issue_728.py',
   'PYMODULE'),
  ('gevent.tests.test__issues461_471',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__issues461_471.py',
   'PYMODULE'),
  ('gevent.tests.test__iwait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__iwait.py',
   'PYMODULE'),
  ('gevent.tests.test__joinall',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__joinall.py',
   'PYMODULE'),
  ('gevent.tests.test__local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__local.py',
   'PYMODULE'),
  ('gevent.tests.test__lock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__lock.py',
   'PYMODULE'),
  ('gevent.tests.test__loop_callback',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__loop_callback.py',
   'PYMODULE'),
  ('gevent.tests.test__makefile_ref',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__makefile_ref.py',
   'PYMODULE'),
  ('gevent.tests.test__memleak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__memleak.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_builtins_future',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_builtins_future.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_hub_in_thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_hub_in_thread.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_logging.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_module_run',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_module_run.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_multiple_imports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_multiple_imports.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_queue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_queue.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_select',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_select.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_selectors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_selectors.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_sigchld.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld_2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_sigchld_2.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld_3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_sigchld_3.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_ssl_warning.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_ssl_warning2.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__monkey_ssl_warning3.py',
   'PYMODULE'),
  ('gevent.tests.test__nondefaultloop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__nondefaultloop.py',
   'PYMODULE'),
  ('gevent.tests.test__order',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__order.py',
   'PYMODULE'),
  ('gevent.tests.test__os',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__os.py',
   'PYMODULE'),
  ('gevent.tests.test__pool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__pool.py',
   'PYMODULE'),
  ('gevent.tests.test__pywsgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__pywsgi.py',
   'PYMODULE'),
  ('gevent.tests.test__queue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__queue.py',
   'PYMODULE'),
  ('gevent.tests.test__real_greenlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__real_greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__refcount',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__refcount.py',
   'PYMODULE'),
  ('gevent.tests.test__refcount_core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__refcount_core.py',
   'PYMODULE'),
  ('gevent.tests.test__resolver_dnspython',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__resolver_dnspython.py',
   'PYMODULE'),
  ('gevent.tests.test__select',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__select.py',
   'PYMODULE'),
  ('gevent.tests.test__selectors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__selectors.py',
   'PYMODULE'),
  ('gevent.tests.test__semaphore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__semaphore.py',
   'PYMODULE'),
  ('gevent.tests.test__server',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__server.py',
   'PYMODULE'),
  ('gevent.tests.test__server_pywsgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__server_pywsgi.py',
   'PYMODULE'),
  ('gevent.tests.test__signal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__signal.py',
   'PYMODULE'),
  ('gevent.tests.test__sleep0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__sleep0.py',
   'PYMODULE'),
  ('gevent.tests.test__socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__socket.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_close',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__socket_close.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_dns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__socket_dns.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_dns6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__socket_dns6.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__socket_errors.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_ex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__socket_ex.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_send_memoryview',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__socket_send_memoryview.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_ssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__socket_ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_timeout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__socket_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__socketpair',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__socketpair.py',
   'PYMODULE'),
  ('gevent.tests.test__ssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__subprocess.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess_interrupted',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__subprocess_interrupted.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess_poll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__subprocess_poll.py',
   'PYMODULE'),
  ('gevent.tests.test__systemerror',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__systemerror.py',
   'PYMODULE'),
  ('gevent.tests.test__thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__thread.py',
   'PYMODULE'),
  ('gevent.tests.test__threading',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__threading.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__threading_2.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_before_monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__threading_before_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_fork_from_dummy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__threading_fork_from_dummy.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_holding_lock_while_monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__threading_holding_lock_while_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_monkey_in_thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__threading_monkey_in_thread.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_native_before_monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__threading_native_before_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_no_monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__threading_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_patched_local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__threading_patched_local.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_vs_settrace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__threading_vs_settrace.py',
   'PYMODULE'),
  ('gevent.tests.test__threadpool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__threadpool.py',
   'PYMODULE'),
  ('gevent.tests.test__threadpool_executor_patched',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__threadpool_executor_patched.py',
   'PYMODULE'),
  ('gevent.tests.test__timeout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\tests\\test__util.py',
   'PYMODULE'),
  ('gevent.thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\thread.py',
   'PYMODULE'),
  ('gevent.threading',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\threading.py',
   'PYMODULE'),
  ('gevent.threadpool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\threadpool.py',
   'PYMODULE'),
  ('gevent.time',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\time.py',
   'PYMODULE'),
  ('gevent.timeout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\timeout.py',
   'PYMODULE'),
  ('gevent.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\util.py',
   'PYMODULE'),
  ('gevent.win32util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\win32util.py',
   'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python311\\Lib\\glob.py', 'PYMODULE'),
  ('greenlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python311\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python311\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Program Files\\Python311\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Program Files\\Python311\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Program Files\\Python311\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Program Files\\Python311\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('html5lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\__init__.py',
   'PYMODULE'),
  ('html5lib._ihatexml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\_ihatexml.py',
   'PYMODULE'),
  ('html5lib._inputstream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\_inputstream.py',
   'PYMODULE'),
  ('html5lib._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\_tokenizer.py',
   'PYMODULE'),
  ('html5lib._trie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\_trie\\__init__.py',
   'PYMODULE'),
  ('html5lib._trie._base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\_trie\\_base.py',
   'PYMODULE'),
  ('html5lib._trie.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\_trie\\py.py',
   'PYMODULE'),
  ('html5lib._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\_utils.py',
   'PYMODULE'),
  ('html5lib.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\constants.py',
   'PYMODULE'),
  ('html5lib.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\filters\\__init__.py',
   'PYMODULE'),
  ('html5lib.filters.alphabeticalattributes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\filters\\alphabeticalattributes.py',
   'PYMODULE'),
  ('html5lib.filters.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\filters\\base.py',
   'PYMODULE'),
  ('html5lib.filters.inject_meta_charset',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\filters\\inject_meta_charset.py',
   'PYMODULE'),
  ('html5lib.filters.optionaltags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\filters\\optionaltags.py',
   'PYMODULE'),
  ('html5lib.filters.sanitizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\filters\\sanitizer.py',
   'PYMODULE'),
  ('html5lib.filters.whitespace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\filters\\whitespace.py',
   'PYMODULE'),
  ('html5lib.html5parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\html5parser.py',
   'PYMODULE'),
  ('html5lib.serializer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\serializer.py',
   'PYMODULE'),
  ('html5lib.treebuilders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\treebuilders\\__init__.py',
   'PYMODULE'),
  ('html5lib.treebuilders.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\treebuilders\\base.py',
   'PYMODULE'),
  ('html5lib.treebuilders.dom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\treebuilders\\dom.py',
   'PYMODULE'),
  ('html5lib.treebuilders.etree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\treebuilders\\etree.py',
   'PYMODULE'),
  ('html5lib.treebuilders.etree_lxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\treebuilders\\etree_lxml.py',
   'PYMODULE'),
  ('html5lib.treewalkers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\treewalkers\\__init__.py',
   'PYMODULE'),
  ('html5lib.treewalkers.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\treewalkers\\base.py',
   'PYMODULE'),
  ('html5lib.treewalkers.dom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\treewalkers\\dom.py',
   'PYMODULE'),
  ('html5lib.treewalkers.etree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\treewalkers\\etree.py',
   'PYMODULE'),
  ('html5lib.treewalkers.etree_lxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\treewalkers\\etree_lxml.py',
   'PYMODULE'),
  ('html5lib.treewalkers.genshi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\html5lib\\treewalkers\\genshi.py',
   'PYMODULE'),
  ('http', 'C:\\Program Files\\Python311\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python311\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python311\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Program Files\\Python311\\Lib\\http\\server.py',
   'PYMODULE'),
  ('imp', 'C:\\Program Files\\Python311\\Lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python311\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program Files\\Python311\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python311\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python311\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python311\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python311\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program Files\\Python311\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python311\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python311\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python311\\Lib\\ipaddress.py', 'PYMODULE'),
  ('itsdangerous',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'C:\\Program Files\\Python311\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python311\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python311\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python311\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python311\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python311\\Lib\\lzma.py', 'PYMODULE'),
  ('mako',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\__init__.py',
   'PYMODULE'),
  ('mako._ast_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\_ast_util.py',
   'PYMODULE'),
  ('mako.ast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\ast.py',
   'PYMODULE'),
  ('mako.cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\cache.py',
   'PYMODULE'),
  ('mako.codegen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\codegen.py',
   'PYMODULE'),
  ('mako.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\compat.py',
   'PYMODULE'),
  ('mako.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\exceptions.py',
   'PYMODULE'),
  ('mako.ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\ext\\__init__.py',
   'PYMODULE'),
  ('mako.ext.pygmentplugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\ext\\pygmentplugin.py',
   'PYMODULE'),
  ('mako.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\filters.py',
   'PYMODULE'),
  ('mako.lexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\lexer.py',
   'PYMODULE'),
  ('mako.parsetree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\parsetree.py',
   'PYMODULE'),
  ('mako.pygen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\pygen.py',
   'PYMODULE'),
  ('mako.pyparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\pyparser.py',
   'PYMODULE'),
  ('mako.runtime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\runtime.py',
   'PYMODULE'),
  ('mako.template',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\template.py',
   'PYMODULE'),
  ('mako.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mako\\util.py',
   'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python311\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('mysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\__init__.py',
   'PYMODULE'),
  ('mysql.connector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.abstracts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\abstracts.py',
   'PYMODULE'),
  ('mysql.connector.authentication',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\authentication.py',
   'PYMODULE'),
  ('mysql.connector.catch23',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\catch23.py',
   'PYMODULE'),
  ('mysql.connector.charsets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\charsets.py',
   'PYMODULE'),
  ('mysql.connector.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\connection.py',
   'PYMODULE'),
  ('mysql.connector.connection_cext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\connection_cext.py',
   'PYMODULE'),
  ('mysql.connector.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\constants.py',
   'PYMODULE'),
  ('mysql.connector.conversion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\conversion.py',
   'PYMODULE'),
  ('mysql.connector.cursor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\cursor.py',
   'PYMODULE'),
  ('mysql.connector.cursor_cext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\cursor_cext.py',
   'PYMODULE'),
  ('mysql.connector.custom_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\custom_types.py',
   'PYMODULE'),
  ('mysql.connector.dbapi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\dbapi.py',
   'PYMODULE'),
  ('mysql.connector.errorcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\errorcode.py',
   'PYMODULE'),
  ('mysql.connector.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\errors.py',
   'PYMODULE'),
  ('mysql.connector.fabric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\fabric\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.fabric.balancing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\fabric\\balancing.py',
   'PYMODULE'),
  ('mysql.connector.fabric.caching',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\fabric\\caching.py',
   'PYMODULE'),
  ('mysql.connector.fabric.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\fabric\\connection.py',
   'PYMODULE'),
  ('mysql.connector.locales',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\locales\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.network',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\network.py',
   'PYMODULE'),
  ('mysql.connector.optionfiles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\optionfiles.py',
   'PYMODULE'),
  ('mysql.connector.pooling',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\pooling.py',
   'PYMODULE'),
  ('mysql.connector.protocol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\protocol.py',
   'PYMODULE'),
  ('mysql.connector.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\utils.py',
   'PYMODULE'),
  ('mysql.connector.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\mysql\\connector\\version.py',
   'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python311\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'C:\\Program Files\\Python311\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python311\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python311\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python311\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'C:\\Program Files\\Python311\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python311\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools',
   'C:\\Program Files\\Python311\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python311\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Program Files\\Python311\\Lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'C:\\Program Files\\Python311\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python311\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psycopg2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\psycopg2\\__init__.py',
   'PYMODULE'),
  ('psycopg2._ipaddress',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\psycopg2\\_ipaddress.py',
   'PYMODULE'),
  ('psycopg2._json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\psycopg2\\_json.py',
   'PYMODULE'),
  ('psycopg2._range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\psycopg2\\_range.py',
   'PYMODULE'),
  ('psycopg2.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\psycopg2\\extensions.py',
   'PYMODULE'),
  ('psycopg2.extras',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\psycopg2\\extras.py',
   'PYMODULE'),
  ('psycopg2.sql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\psycopg2\\sql.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Program Files\\Python311\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Program Files\\Python311\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Program Files\\Python311\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program Files\\Python311\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pymysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql._auth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.charset',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.connections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.converters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.err',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.times',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pywin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python311\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python311\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python311\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter',
   'C:\\Program Files\\Python311\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'C:\\Program Files\\Python311\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Program Files\\Python311\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python311\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Program Files\\Python311\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python311\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python311\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python311\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\Program Files\\Python311\\Lib\\site.py', 'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\six.py',
   'PYMODULE'),
  ('smtplib', 'C:\\Program Files\\Python311\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python311\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'C:\\Program Files\\Python311\\Lib\\socketserver.py',
   'PYMODULE'),
  ('soupsieve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlalchemy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.aioodbc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\connectors\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\connectors\\asyncio.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.aioodbc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mssql\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.vector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\oracle\\vector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.operators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.strategies',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Program Files\\Python311\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Program Files\\Python311\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Program Files\\Python311\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python311\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'C:\\Program Files\\Python311\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python311\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Program Files\\Python311\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program Files\\Python311\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig', 'C:\\Program Files\\Python311\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python311\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python311\\Lib\\tempfile.py', 'PYMODULE'),
  ('test', 'C:\\Program Files\\Python311\\Lib\\test\\__init__.py', 'PYMODULE'),
  ('test.libregrtest',
   'C:\\Program Files\\Python311\\Lib\\test\\libregrtest\\__init__.py',
   'PYMODULE'),
  ('test.libregrtest.cmdline',
   'C:\\Program Files\\Python311\\Lib\\test\\libregrtest\\cmdline.py',
   'PYMODULE'),
  ('test.libregrtest.main',
   'C:\\Program Files\\Python311\\Lib\\test\\libregrtest\\main.py',
   'PYMODULE'),
  ('test.libregrtest.pgo',
   'C:\\Program Files\\Python311\\Lib\\test\\libregrtest\\pgo.py',
   'PYMODULE'),
  ('test.libregrtest.refleak',
   'C:\\Program Files\\Python311\\Lib\\test\\libregrtest\\refleak.py',
   'PYMODULE'),
  ('test.libregrtest.runtest',
   'C:\\Program Files\\Python311\\Lib\\test\\libregrtest\\runtest.py',
   'PYMODULE'),
  ('test.libregrtest.runtest_mp',
   'C:\\Program Files\\Python311\\Lib\\test\\libregrtest\\runtest_mp.py',
   'PYMODULE'),
  ('test.libregrtest.save_env',
   'C:\\Program Files\\Python311\\Lib\\test\\libregrtest\\save_env.py',
   'PYMODULE'),
  ('test.libregrtest.setup',
   'C:\\Program Files\\Python311\\Lib\\test\\libregrtest\\setup.py',
   'PYMODULE'),
  ('test.libregrtest.utils',
   'C:\\Program Files\\Python311\\Lib\\test\\libregrtest\\utils.py',
   'PYMODULE'),
  ('test.libregrtest.win_utils',
   'C:\\Program Files\\Python311\\Lib\\test\\libregrtest\\win_utils.py',
   'PYMODULE'),
  ('test.lock_tests',
   'C:\\Program Files\\Python311\\Lib\\test\\lock_tests.py',
   'PYMODULE'),
  ('test.support',
   'C:\\Program Files\\Python311\\Lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('test.support.import_helper',
   'C:\\Program Files\\Python311\\Lib\\test\\support\\import_helper.py',
   'PYMODULE'),
  ('test.support.os_helper',
   'C:\\Program Files\\Python311\\Lib\\test\\support\\os_helper.py',
   'PYMODULE'),
  ('test.support.testresult',
   'C:\\Program Files\\Python311\\Lib\\test\\support\\testresult.py',
   'PYMODULE'),
  ('test.support.threading_helper',
   'C:\\Program Files\\Python311\\Lib\\test\\support\\threading_helper.py',
   'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python311\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python311\\Lib\\threading.py', 'PYMODULE'),
  ('timeit', 'C:\\Program Files\\Python311\\Lib\\timeit.py', 'PYMODULE'),
  ('tkinter',
   'C:\\Program Files\\Python311\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Program Files\\Python311\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Program Files\\Python311\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Program Files\\Python311\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Program Files\\Python311\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'C:\\Program Files\\Python311\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python311\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib',
   'C:\\Program Files\\Python311\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Program Files\\Python311\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Program Files\\Python311\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Program Files\\Python311\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('trace', 'C:\\Program Files\\Python311\\Lib\\trace.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python311\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'C:\\Program Files\\Python311\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python311\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('tzdata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('tzlocal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzlocal\\__init__.py',
   'PYMODULE'),
  ('tzlocal.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzlocal\\unix.py',
   'PYMODULE'),
  ('tzlocal.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzlocal\\utils.py',
   'PYMODULE'),
  ('tzlocal.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzlocal\\win32.py',
   'PYMODULE'),
  ('tzlocal.windows_tz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzlocal\\windows_tz.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Program Files\\Python311\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Program Files\\Python311\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Program Files\\Python311\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Program Files\\Python311\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Program Files\\Python311\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Program Files\\Python311\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Program Files\\Python311\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Program Files\\Python311\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Program Files\\Python311\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Program Files\\Python311\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Program Files\\Python311\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Program Files\\Python311\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python311\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files\\Python311\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python311\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files\\Python311\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files\\Python311\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid', 'C:\\Program Files\\Python311\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'C:\\Program Files\\Python311\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('webencodings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\webencodings\\__init__.py',
   'PYMODULE'),
  ('webencodings.labels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\webencodings\\labels.py',
   'PYMODULE'),
  ('webencodings.x_user_defined',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\webencodings\\x_user_defined.py',
   'PYMODULE'),
  ('werkzeug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('win32com',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.shell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32traceutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('wsgiref',
   'C:\\Program Files\\Python311\\Lib\\wsgiref\\__init__.py',
   'PYMODULE'),
  ('wsgiref.validate',
   'C:\\Program Files\\Python311\\Lib\\wsgiref\\validate.py',
   'PYMODULE'),
  ('xlsxwriter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE'),
  ('xlsxwriter.app',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE'),
  ('xlsxwriter.chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE'),
  ('xlsxwriter.chart_area',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE'),
  ('xlsxwriter.chart_bar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_column',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE'),
  ('xlsxwriter.chart_doughnut',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE'),
  ('xlsxwriter.chart_line',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE'),
  ('xlsxwriter.chart_pie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE'),
  ('xlsxwriter.chart_radar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_scatter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE'),
  ('xlsxwriter.chart_stock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE'),
  ('xlsxwriter.chartsheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE'),
  ('xlsxwriter.color',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\color.py',
   'PYMODULE'),
  ('xlsxwriter.comments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE'),
  ('xlsxwriter.contenttypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE'),
  ('xlsxwriter.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE'),
  ('xlsxwriter.custom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE'),
  ('xlsxwriter.drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE'),
  ('xlsxwriter.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE'),
  ('xlsxwriter.feature_property_bag',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE'),
  ('xlsxwriter.format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE'),
  ('xlsxwriter.image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE'),
  ('xlsxwriter.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE'),
  ('xlsxwriter.packager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE'),
  ('xlsxwriter.relationships',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_rel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_structure',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE'),
  ('xlsxwriter.shape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE'),
  ('xlsxwriter.sharedstrings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE'),
  ('xlsxwriter.styles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE'),
  ('xlsxwriter.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE'),
  ('xlsxwriter.theme',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE'),
  ('xlsxwriter.url',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE'),
  ('xlsxwriter.utility',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE'),
  ('xlsxwriter.vml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE'),
  ('xlsxwriter.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE'),
  ('xlsxwriter.worksheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE'),
  ('xlsxwriter.xmlwriter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE'),
  ('xml', 'C:\\Program Files\\Python311\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'C:\\Program Files\\Python311\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Program Files\\Python311\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Program Files\\Python311\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Program Files\\Python311\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Program Files\\Python311\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Program Files\\Python311\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Program Files\\Python311\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Program Files\\Python311\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Program Files\\Python311\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Program Files\\Python311\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Program Files\\Python311\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Program Files\\Python311\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Program Files\\Python311\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files\\Python311\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files\\Python311\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files\\Python311\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files\\Python311\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files\\Python311\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files\\Python311\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files\\Python311\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files\\Python311\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program Files\\Python311\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program Files\\Python311\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'C:\\Program Files\\Python311\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python311\\Lib\\zipimport.py', 'PYMODULE'),
  ('zoneinfo',
   'C:\\Program Files\\Python311\\Lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'C:\\Program Files\\Python311\\Lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'C:\\Program Files\\Python311\\Lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'C:\\Program Files\\Python311\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE'),
  ('zope', '-', 'PYMODULE'),
  ('zope.event',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope\\event\\__init__.py',
   'PYMODULE'),
  ('zope.interface',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope\\interface\\__init__.py',
   'PYMODULE'),
  ('zope.interface._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope\\interface\\_compat.py',
   'PYMODULE'),
  ('zope.interface.declarations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope\\interface\\declarations.py',
   'PYMODULE'),
  ('zope.interface.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope\\interface\\exceptions.py',
   'PYMODULE'),
  ('zope.interface.interface',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope\\interface\\interface.py',
   'PYMODULE'),
  ('zope.interface.interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope\\interface\\interfaces.py',
   'PYMODULE'),
  ('zope.interface.ro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope\\interface\\ro.py',
   'PYMODULE'),
  ('zope.interface.verify',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope\\interface\\verify.py',
   'PYMODULE')])
