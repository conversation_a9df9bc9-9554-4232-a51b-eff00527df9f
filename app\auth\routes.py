# -*- coding: utf-8 -*-
"""
Authentication routes
"""

from flask import render_template, request, jsonify, flash, redirect, url_for, session
from flask_login import login_user, logout_user, login_required, current_user
from app.auth import bp
from app.models import User
from app.extensions import db
from datetime import datetime


@bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form
            username = data.get('username', '').strip()
            password = data.get('password', '')
            remember_me = data.get('remember_me', False)
            
            if not username or not password:
                error_msg = 'يرجى إدخال اسم المستخدم وكلمة المرور'
                if request.is_json:
                    return jsonify({'error': error_msg}), 400
                flash(error_msg, 'error')
                return render_template('auth/login.html')
            
            # Find user by username or email
            user = User.query.filter(
                (User.username == username) | (User.email == username)
            ).first()
            
            if user and user.check_password(password) and user.is_active:
                login_user(user, remember=remember_me)
                user.update_last_login()
                
                # Get next page from request args
                next_page = request.args.get('next')
                if not next_page or not next_page.startswith('/'):
                    next_page = url_for('main.index')
                
                if request.is_json:
                    return jsonify({
                        'success': True,
                        'redirect': next_page,
                        'message': f'مرحباً {user.full_name}'
                    })
                
                flash(f'مرحباً {user.full_name}', 'success')
                return redirect(next_page)
            else:
                error_msg = 'اسم المستخدم أو كلمة المرور غير صحيحة'
                if request.is_json:
                    return jsonify({'error': error_msg}), 401
                flash(error_msg, 'error')
                
        except Exception as e:
            error_msg = f'حدث خطأ: {str(e)}'
            if request.is_json:
                return jsonify({'error': error_msg}), 500
            flash(error_msg, 'error')
    
    return render_template('auth/login.html')


@bp.route('/logout')
@login_required
def logout():
    """User logout"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('auth.login'))


@bp.route('/register', methods=['GET', 'POST'])
@login_required
def register():
    """Register new user (admin only)"""
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form
            
            # Validate required fields
            required_fields = ['username', 'email', 'password', 'full_name']
            for field in required_fields:
                if not data.get(field, '').strip():
                    error_msg = f'حقل {field} مطلوب'
                    if request.is_json:
                        return jsonify({'error': error_msg}), 400
                    flash(error_msg, 'error')
                    return render_template('auth/register.html')
            
            username = data['username'].strip()
            email = data['email'].strip()
            password = data['password']
            full_name = data['full_name'].strip()
            is_admin = data.get('is_admin', False)
            
            # Check if username already exists
            if User.query.filter_by(username=username).first():
                error_msg = 'اسم المستخدم موجود مسبقاً'
                if request.is_json:
                    return jsonify({'error': error_msg}), 400
                flash(error_msg, 'error')
                return render_template('auth/register.html')
            
            # Check if email already exists
            if User.query.filter_by(email=email).first():
                error_msg = 'البريد الإلكتروني موجود مسبقاً'
                if request.is_json:
                    return jsonify({'error': error_msg}), 400
                flash(error_msg, 'error')
                return render_template('auth/register.html')
            
            # Create new user
            user = User(
                username=username,
                email=email,
                full_name=full_name,
                is_admin=is_admin
            )
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            success_msg = f'تم إنشاء المستخدم {username} بنجاح'
            if request.is_json:
                return jsonify({'success': True, 'message': success_msg})
            
            flash(success_msg, 'success')
            return redirect(url_for('auth.register'))
            
        except Exception as e:
            db.session.rollback()
            error_msg = f'حدث خطأ: {str(e)}'
            if request.is_json:
                return jsonify({'error': error_msg}), 500
            flash(error_msg, 'error')
    
    return render_template('auth/register.html')


@bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change user password"""
    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form
            
            current_password = data.get('current_password', '')
            new_password = data.get('new_password', '')
            confirm_password = data.get('confirm_password', '')
            
            # Validate inputs
            if not all([current_password, new_password, confirm_password]):
                error_msg = 'جميع الحقول مطلوبة'
                if request.is_json:
                    return jsonify({'error': error_msg}), 400
                flash(error_msg, 'error')
                return render_template('auth/change_password.html')
            
            # Check current password
            if not current_user.check_password(current_password):
                error_msg = 'كلمة المرور الحالية غير صحيحة'
                if request.is_json:
                    return jsonify({'error': error_msg}), 400
                flash(error_msg, 'error')
                return render_template('auth/change_password.html')
            
            # Check password confirmation
            if new_password != confirm_password:
                error_msg = 'كلمة المرور الجديدة وتأكيدها غير متطابقتين'
                if request.is_json:
                    return jsonify({'error': error_msg}), 400
                flash(error_msg, 'error')
                return render_template('auth/change_password.html')
            
            # Check password length
            if len(new_password) < 6:
                error_msg = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
                if request.is_json:
                    return jsonify({'error': error_msg}), 400
                flash(error_msg, 'error')
                return render_template('auth/change_password.html')
            
            # Update password
            current_user.set_password(new_password)
            current_user.updated_at = datetime.utcnow()
            db.session.commit()
            
            success_msg = 'تم تغيير كلمة المرور بنجاح'
            if request.is_json:
                return jsonify({'success': True, 'message': success_msg})
            
            flash(success_msg, 'success')
            return redirect(url_for('main.index'))
            
        except Exception as e:
            db.session.rollback()
            error_msg = f'حدث خطأ: {str(e)}'
            if request.is_json:
                return jsonify({'error': error_msg}), 500
            flash(error_msg, 'error')
    
    return render_template('auth/change_password.html')


@bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """User profile and notification settings"""
    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form
            
            # Update notification preferences
            current_user.email_notifications = data.get('email_notifications', False)
            current_user.sms_notifications = data.get('sms_notifications', False)
            current_user.notification_days_before = int(data.get('notification_days_before', 15))
            current_user.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            success_msg = 'تم تحديث الإعدادات بنجاح'
            if request.is_json:
                return jsonify({'success': True, 'message': success_msg})
            
            flash(success_msg, 'success')
            
        except Exception as e:
            db.session.rollback()
            error_msg = f'حدث خطأ: {str(e)}'
            if request.is_json:
                return jsonify({'error': error_msg}), 500
            flash(error_msg, 'error')
    
    return render_template('auth/profile.html')


@bp.route('/api/check-username')
@login_required
def check_username():
    """API endpoint to check if username is available"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403
    
    username = request.args.get('username', '').strip()
    exclude_id = request.args.get('exclude_id', type=int)
    
    if not username:
        return jsonify({'error': 'اسم المستخدم مطلوب'}), 400
    
    query = User.query.filter_by(username=username)
    if exclude_id:
        query = query.filter(User.id != exclude_id)
    
    exists = query.first() is not None
    return jsonify({'exists': exists})


@bp.route('/api/check-email')
@login_required
def check_email():
    """API endpoint to check if email is available"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403
    
    email = request.args.get('email', '').strip()
    exclude_id = request.args.get('exclude_id', type=int)
    
    if not email:
        return jsonify({'error': 'البريد الإلكتروني مطلوب'}), 400
    
    query = User.query.filter_by(email=email)
    if exclude_id:
        query = query.filter(User.id != exclude_id)
    
    exists = query.first() is not None
    return jsonify({'exists': exists})


@bp.route('/users')
@login_required
def users():
    """User management page (admin only)"""
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('main.index'))

    # Get all users with pagination
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '', type=str)

    query = User.query

    if search:
        query = query.filter(
            db.or_(
                User.username.contains(search),
                User.full_name.contains(search),
                User.email.contains(search)
            )
        )

    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('auth/users.html', users=users, search=search)


@bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    """Edit user (admin only)"""
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('main.index'))

    user = User.query.get_or_404(user_id)

    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form

            # Validate required fields
            required_fields = ['username', 'email', 'full_name']
            if not all(field in data and data[field].strip() for field in required_fields):
                error_msg = 'جميع الحقول مطلوبة'
                if request.is_json:
                    return jsonify({'error': error_msg}), 400
                flash(error_msg, 'error')
                return render_template('auth/edit_user.html', user=user)

            username = data['username'].strip()
            email = data['email'].strip()
            full_name = data['full_name'].strip()
            is_admin = data.get('is_admin', False)
            is_active = data.get('is_active', True)

            # Check if username already exists (excluding current user)
            existing_user = User.query.filter(User.username == username, User.id != user_id).first()
            if existing_user:
                error_msg = 'اسم المستخدم موجود مسبقاً'
                if request.is_json:
                    return jsonify({'error': error_msg}), 400
                flash(error_msg, 'error')
                return render_template('auth/edit_user.html', user=user)

            # Check if email already exists (excluding current user)
            existing_email = User.query.filter(User.email == email, User.id != user_id).first()
            if existing_email:
                error_msg = 'البريد الإلكتروني موجود مسبقاً'
                if request.is_json:
                    return jsonify({'error': error_msg}), 400
                flash(error_msg, 'error')
                return render_template('auth/edit_user.html', user=user)

            # Update user
            user.username = username
            user.email = email
            user.full_name = full_name
            user.is_admin = is_admin
            user.is_active = is_active
            user.updated_at = datetime.utcnow()

            # Update notification preferences if provided
            if 'email_notifications' in data:
                user.email_notifications = data.get('email_notifications', False)
            if 'sms_notifications' in data:
                user.sms_notifications = data.get('sms_notifications', False)
            if 'notification_days_before' in data:
                user.notification_days_before = int(data.get('notification_days_before', 15))

            db.session.commit()

            success_msg = f'تم تحديث المستخدم {username} بنجاح'
            if request.is_json:
                return jsonify({'success': True, 'message': success_msg})

            flash(success_msg, 'success')
            return redirect(url_for('auth.users'))

        except Exception as e:
            db.session.rollback()
            error_msg = f'حدث خطأ: {str(e)}'
            if request.is_json:
                return jsonify({'error': error_msg}), 500
            flash(error_msg, 'error')

    return render_template('auth/edit_user.html', user=user)


@bp.route('/users/<int:user_id>/reset-password', methods=['POST'])
@login_required
def reset_user_password(user_id):
    """Reset user password (admin only)"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403

    user = User.query.get_or_404(user_id)

    try:
        data = request.get_json()
        new_password = data.get('new_password', '')

        if len(new_password) < 6:
            return jsonify({'error': 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'}), 400

        user.set_password(new_password)
        user.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({'success': True, 'message': f'تم إعادة تعيين كلمة مرور {user.username} بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500


@bp.route('/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
def toggle_user_status(user_id):
    """Toggle user active status (admin only)"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403

    user = User.query.get_or_404(user_id)

    # Prevent admin from deactivating themselves
    if user.id == current_user.id:
        return jsonify({'error': 'لا يمكنك إلغاء تفعيل حسابك الخاص'}), 400

    try:
        user.is_active = not user.is_active
        user.updated_at = datetime.utcnow()
        db.session.commit()

        status = 'تم تفعيل' if user.is_active else 'تم إلغاء تفعيل'
        return jsonify({'success': True, 'message': f'{status} المستخدم {user.username} بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500
