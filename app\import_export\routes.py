# -*- coding: utf-8 -*-
"""
Import/Export routes
"""

from flask import request, jsonify, flash, redirect, url_for, current_app
from flask_login import login_required
from app.import_export import bp
from app.models import Subscriber, Subscription
from app.extensions import db
from datetime import datetime, date
import pandas as pd
import os
from werkzeug.utils import secure_filename

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

@bp.route('/excel', methods=['POST'])
@login_required
def import_excel():
    """Import data from Excel file"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({'error': 'لم يتم اختيار ملف'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'لم يتم اختيار ملف'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'نوع الملف غير مدعوم. يرجى استخدام ملفات Excel (.xlsx, .xls)'}), 400
        
        # Save uploaded file temporarily
        filename = secure_filename(file.filename)
        upload_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        file.save(upload_path)
        
        # Read Excel file
        try:
            df = pd.read_excel(upload_path)
        except Exception as e:
            os.remove(upload_path)  # Clean up
            return jsonify({'error': f'خطأ في قراءة ملف Excel: {str(e)}'}), 400
        
        # Validate required columns
        required_columns = [
            'رقم الصندوق', 'اسم المشترك', 'رقم الهاتف', 
            'رقم الهوية/الجواز', 'حجم الصندوق'
        ]
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            os.remove(upload_path)  # Clean up
            return jsonify({
                'error': f'الأعمدة التالية مفقودة: {", ".join(missing_columns)}'
            }), 400
        
        # Process data
        results = {
            'total_rows': len(df),
            'successful': 0,
            'failed': 0,
            'errors': []
        }
        
        for index, row in df.iterrows():
            try:
                # Skip empty rows
                if pd.isna(row['رقم الصندوق']) or pd.isna(row['اسم المشترك']):
                    continue
                
                # Check if subscriber already exists
                existing_box = Subscriber.query.filter_by(
                    box_number=str(row['رقم الصندوق']).strip()
                ).first()
                
                existing_passport = Subscriber.query.filter_by(
                    passport_number=str(row['رقم الهوية/الجواز']).strip()
                ).first()
                
                if existing_box:
                    results['errors'].append(f'الصف {index + 2}: رقم الصندوق {row["رقم الصندوق"]} موجود مسبقاً')
                    results['failed'] += 1
                    continue
                
                if existing_passport:
                    results['errors'].append(f'الصف {index + 2}: رقم الهوية/الجواز {row["رقم الهوية/الجواز"]} موجود مسبقاً')
                    results['failed'] += 1
                    continue
                
                # Create subscriber
                subscriber = Subscriber(
                    box_number=str(row['رقم الصندوق']).strip(),
                    name=str(row['اسم المشترك']).strip(),
                    phone=str(row['رقم الهاتف']).strip(),
                    address=str(row.get('العنوان', '')).strip() if pd.notna(row.get('العنوان')) else '',
                    passport_number=str(row['رقم الهوية/الجواز']).strip(),
                    box_size=str(row['حجم الصندوق']).strip(),
                    email=str(row.get('البريد الإلكتروني', '')).strip() if pd.notna(row.get('البريد الإلكتروني')) else ''
                )
                
                db.session.add(subscriber)
                db.session.flush()  # Get the ID
                
                # Add subscription if dates are provided
                if 'تاريخ البدء' in df.columns and 'تاريخ الانتهاء' in df.columns:
                    start_date = row.get('تاريخ البدء')
                    end_date = row.get('تاريخ الانتهاء')
                    
                    if pd.notna(start_date) and pd.notna(end_date):
                        try:
                            # Convert to date objects
                            if isinstance(start_date, str):
                                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                            elif hasattr(start_date, 'date'):
                                start_date = start_date.date()
                            
                            if isinstance(end_date, str):
                                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                            elif hasattr(end_date, 'date'):
                                end_date = end_date.date()
                            
                            # Get payment info
                            amount_paid = row.get('المبلغ المدفوع', 0)
                            if pd.isna(amount_paid):
                                amount_paid = 0
                            
                            payment_date = row.get('تاريخ الدفع')
                            if pd.notna(payment_date):
                                if isinstance(payment_date, str):
                                    payment_date = datetime.strptime(payment_date, '%Y-%m-%d').date()
                                elif hasattr(payment_date, 'date'):
                                    payment_date = payment_date.date()
                            else:
                                payment_date = None
                            
                            subscription = Subscription(
                                subscriber_id=subscriber.id,
                                start_date=start_date,
                                end_date=end_date,
                                amount_paid=float(amount_paid),
                                payment_date=payment_date,
                                notes=str(row.get('ملاحظات', '')).strip() if pd.notna(row.get('ملاحظات')) else '',
                                is_active=True
                            )
                            
                            db.session.add(subscription)
                            
                        except Exception as e:
                            results['errors'].append(f'الصف {index + 2}: خطأ في بيانات الاشتراك - {str(e)}')
                
                results['successful'] += 1
                
            except Exception as e:
                results['errors'].append(f'الصف {index + 2}: {str(e)}')
                results['failed'] += 1
                db.session.rollback()
                continue
        
        # Commit all changes
        db.session.commit()
        
        # Clean up uploaded file
        os.remove(upload_path)
        
        return jsonify({
            'success': True,
            'message': f'تم استيراد {results["successful"]} مشترك بنجاح',
            'results': results
        })
        
    except Exception as e:
        db.session.rollback()
        # Clean up uploaded file if it exists
        if 'upload_path' in locals() and os.path.exists(upload_path):
            os.remove(upload_path)
        
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

@bp.route('/template')
@login_required
def download_template():
    """Download Excel template for import"""
    try:
        # Create template DataFrame
        template_data = {
            'رقم الصندوق': ['001', '002', '003'],
            'اسم المشترك': ['أحمد محمد', 'فاطمة علي', 'محمد حسن'],
            'رقم الهاتف': ['0501234567', '0509876543', '0551234567'],
            'العنوان': ['الرياض، حي النخيل', 'جدة، حي الصفا', 'الدمام، حي الفيصلية'],
            'رقم الهوية/الجواز': ['1234567890', '0987654321', '1122334455'],
            'حجم الصندوق': ['Small', 'Medium', 'Large'],
            'البريد الإلكتروني': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'تاريخ البدء': ['2024-01-01', '2024-01-15', '2024-02-01'],
            'تاريخ الانتهاء': ['2024-12-31', '2025-01-14', '2025-01-31'],
            'المبلغ المدفوع': [500.0, 750.0, 1000.0],
            'تاريخ الدفع': ['2024-01-01', '2024-01-15', '2024-02-01'],
            'ملاحظات': ['اشتراك سنوي', 'تجديد', 'مشترك جديد']
        }
        
        df = pd.DataFrame(template_data)
        
        # Create Excel file in memory
        from io import BytesIO
        output = BytesIO()
        
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='قالب البيانات', index=False)
            
            # Get workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['قالب البيانات']
            
            # Add formatting
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })
            
            required_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#FFE6E6',
                'border': 1
            })
            
            # Format headers
            for col_num, value in enumerate(df.columns.values):
                if value in ['رقم الصندوق', 'اسم المشترك', 'رقم الهاتف', 'رقم الهوية/الجواز', 'حجم الصندوق']:
                    worksheet.write(0, col_num, value, required_format)
                else:
                    worksheet.write(0, col_num, value, header_format)
            
            # Auto-adjust column widths
            for i, col in enumerate(df.columns):
                max_len = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, max_len)
            
            # Add instructions
            worksheet.write(len(df) + 2, 0, 'تعليمات:', workbook.add_format({'bold': True}))
            worksheet.write(len(df) + 3, 0, '1. الأعمدة ذات الخلفية الحمراء مطلوبة')
            worksheet.write(len(df) + 4, 0, '2. تنسيق التواريخ: YYYY-MM-DD (مثال: 2024-01-01)')
            worksheet.write(len(df) + 5, 0, '3. أحجام الصناديق المسموحة: Small, Medium, Large')
            worksheet.write(len(df) + 6, 0, '4. يجب أن يكون رقم الصندوق ورقم الهوية/الجواز فريدين')
        
        output.seek(0)
        
        from flask import send_file
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='template_postal_boxes.xlsx'
        )
        
    except Exception as e:
        return jsonify({'error': f'حدث خطأ في إنشاء القالب: {str(e)}'}), 500
