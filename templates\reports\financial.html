{% extends "base.html" %}

{% block title %}التقارير المالية - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">التقارير المالية</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('reports.export_excel', type='financial', start_date=start_date, end_date=end_date) }}" 
               class="btn btn-success">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </a>
            <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i> العودة
            </a>
        </div>
    </div>
</div>

<!-- Date Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="start_date" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> تطبيق الفلتر
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            إجمالي الإيرادات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_revenue | currency }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            عدد الاشتراكات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_subscriptions }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            الفترة الزمنية
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800">
                            {{ start_date }} إلى {{ end_date }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Monthly Revenue Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">الإيرادات الشهرية</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="monthlyRevenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Box Size Revenue Distribution -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">الإيرادات حسب حجم الصندوق</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="boxSizeRevenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">تفاصيل المدفوعات</h6>
    </div>
    <div class="card-body">
        {% if subscriptions %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>رقم الصندوق</th>
                            <th>اسم المشترك</th>
                            <th>حجم الصندوق</th>
                            <th>تاريخ البدء</th>
                            <th>تاريخ الانتهاء</th>
                            <th>تاريخ الدفع</th>
                            <th>المبلغ المدفوع</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for subscription in subscriptions %}
                            <tr>
                                <td>{{ subscription.subscriber.box_number }}</td>
                                <td>{{ subscription.subscriber.name }}</td>
                                <td>
                                    <span class="badge bg-info">{{ subscription.subscriber.box_size }}</span>
                                </td>
                                <td>{{ subscription.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ subscription.end_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if subscription.payment_date %}
                                        {{ subscription.payment_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ subscription.amount_paid | currency }}</strong>
                                </td>
                                <td>{{ subscription.notes or '-' }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="table-success">
                            <th colspan="6">الإجمالي</th>
                            <th>{{ total_revenue | currency }}</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد بيانات مالية</h5>
                <p class="text-muted">لا توجد مدفوعات في الفترة المحددة</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Monthly Summary Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">ملخص شهري</h6>
    </div>
    <div class="card-body">
        {% if monthly_revenue %}
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>الشهر</th>
                            <th>عدد الاشتراكات</th>
                            <th>إجمالي الإيرادات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for month, data in monthly_revenue.items() %}
                            <tr>
                                <td>{{ month }}</td>
                                <td>{{ data.count }}</td>
                                <td>{{ data.revenue | currency }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <p class="text-muted">لا توجد بيانات شهرية</p>
        {% endif %}
    </div>
</div>

<!-- Box Size Summary -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">ملخص حسب حجم الصندوق</h6>
    </div>
    <div class="card-body">
        {% if box_size_revenue %}
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>حجم الصندوق</th>
                            <th>عدد الاشتراكات</th>
                            <th>إجمالي الإيرادات</th>
                            <th>النسبة من الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for size, data in box_size_revenue.items() %}
                            <tr>
                                <td>
                                    <span class="badge bg-info">{{ size | box_size_arabic }}</span>
                                </td>
                                <td>{{ data.count }}</td>
                                <td>{{ data.revenue | currency }}</td>
                                <td>
                                    {% set percentage = (data.revenue / total_revenue * 100) if total_revenue > 0 else 0 %}
                                    {{ "{:.1f}".format(percentage) }}%
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <p class="text-muted">لا توجد بيانات حسب حجم الصندوق</p>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Monthly Revenue Chart
const monthlyRevenueData = {{ monthly_revenue | tojson }};
const monthlyCtx = document.getElementById('monthlyRevenueChart').getContext('2d');

new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: Object.keys(monthlyRevenueData),
        datasets: [{
            label: 'الإيرادات (دينار)',
            data: Object.values(monthlyRevenueData).map(item => item.revenue),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'الإيرادات الشهرية'
            },
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' دينار';
                    }
                }
            }
        }
    }
});

// Box Size Revenue Chart
const boxSizeRevenueData = {{ box_size_revenue | tojson }};
const boxSizeCtx = document.getElementById('boxSizeRevenueChart').getContext('2d');

new Chart(boxSizeCtx, {
    type: 'doughnut',
    data: {
        labels: Object.keys(boxSizeRevenueData),
        datasets: [{
            data: Object.values(boxSizeRevenueData).map(item => item.revenue),
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'توزيع الإيرادات حسب حجم الصندوق'
            },
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': ' + context.parsed.toLocaleString() + ' دينار';
                    }
                }
            }
        }
    }
});
</script>
{% endblock %}
