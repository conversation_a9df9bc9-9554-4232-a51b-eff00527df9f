{% extends "base.html" %}

{% block title %}تعديل المستخدم - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-edit me-2"></i>تعديل المستخدم: {{ user.full_name }}</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('auth.users') }}">إدارة المستخدمين</a></li>
                        <li class="breadcrumb-item active">تعديل المستخدم</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- معلومات المستخدم الأساسية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-id-card me-2"></i>المعلومات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <form id="editUserForm" method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">اسم المستخدم *</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ user.username }}" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ user.email }}" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="{{ user.full_name }}" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الصلاحيات</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_admin" name="is_admin" 
                                           {{ 'checked' if user.is_admin }}>
                                    <label class="form-check-label" for="is_admin">
                                        <i class="fas fa-crown me-1"></i>مدير النظام
                                    </label>
                                    <div class="form-text">المديرون لديهم صلاحية كاملة على النظام</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">حالة الحساب</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {{ 'checked' if user.is_active }}>
                                    <label class="form-check-label" for="is_active">
                                        <i class="fas fa-check-circle me-1"></i>حساب نشط
                                    </label>
                                    <div class="form-text">الحسابات غير النشطة لا يمكنها تسجيل الدخول</div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إعدادات الإشعارات -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell me-2"></i>إعدادات الإشعارات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="email_notifications" 
                                       name="email_notifications" form="editUserForm"
                                       {{ 'checked' if user.email_notifications }}>
                                <label class="form-check-label" for="email_notifications">
                                    <i class="fas fa-envelope me-1"></i>إشعارات البريد الإلكتروني
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sms_notifications" 
                                       name="sms_notifications" form="editUserForm"
                                       {{ 'checked' if user.sms_notifications }}>
                                <label class="form-check-label" for="sms_notifications">
                                    <i class="fas fa-sms me-1"></i>الرسائل النصية
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notification_days_before" class="form-label">أيام الإشعار قبل انتهاء الاشتراك</label>
                        <select class="form-select" id="notification_days_before" name="notification_days_before" form="editUserForm">
                            <option value="1" {{ 'selected' if user.notification_days_before == 1 }}>يوم واحد</option>
                            <option value="3" {{ 'selected' if user.notification_days_before == 3 }}>3 أيام</option>
                            <option value="7" {{ 'selected' if user.notification_days_before == 7 }}>أسبوع</option>
                            <option value="15" {{ 'selected' if user.notification_days_before == 15 }}>15 يوم</option>
                            <option value="30" {{ 'selected' if user.notification_days_before == 30 }}>شهر</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <button type="submit" form="editUserForm" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                        <button type="button" class="btn btn-warning" onclick="showResetPasswordModal()">
                            <i class="fas fa-key me-2"></i>إعادة تعيين كلمة المرور
                        </button>
                        <a href="{{ url_for('auth.users') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- معلومات إضافية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات الحساب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-user-circle fa-5x text-primary"></i>
                        <h6 class="mt-2">{{ user.full_name }}</h6>
                        <small class="text-muted">@{{ user.username }}</small>
                    </div>
                    
                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">تاريخ الإنشاء</small>
                            <div>{{ user.created_at.strftime('%Y-%m-%d') }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">آخر تحديث</small>
                            <div>{{ user.updated_at.strftime('%Y-%m-%d') }}</div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <small class="text-muted">آخر دخول</small>
                        <div>
                            {% if user.last_login %}
                                {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                                لم يسجل دخول من قبل
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>الصلاحيات:</span>
                        {% if user.is_admin %}
                            <span class="badge bg-warning text-dark">مدير</span>
                        {% else %}
                            <span class="badge bg-secondary">مستخدم</span>
                        {% endif %}
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>الحالة:</span>
                        {% if user.is_active %}
                            <span class="badge bg-success">نشط</span>
                        {% else %}
                            <span class="badge bg-danger">معطل</span>
                        {% endif %}
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>إشعارات البريد:</span>
                        <span class="badge bg-{{ 'success' if user.email_notifications else 'secondary' }}">
                            {{ 'مفعلة' if user.email_notifications else 'معطلة' }}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>الرسائل النصية:</span>
                        <span class="badge bg-{{ 'success' if user.sms_notifications else 'secondary' }}">
                            {{ 'مفعلة' if user.sms_notifications else 'معطلة' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإعادة تعيين كلمة المرور -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعادة تعيين كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    سيتم إعادة تعيين كلمة مرور المستخدم <strong>{{ user.username }}</strong>
                </div>
                <form id="resetPasswordForm">
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="newPassword" required minlength="6">
                        <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="submitResetPassword()">
                    <i class="fas fa-key me-2"></i>إعادة تعيين
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form submission
document.getElementById('editUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {};
    
    // Convert FormData to object
    for (let [key, value] of formData.entries()) {
        if (key === 'is_admin' || key === 'is_active' || key === 'email_notifications' || key === 'sms_notifications') {
            data[key] = true;
        } else {
            data[key] = value;
        }
    }
    
    // Add unchecked checkboxes as false
    if (!formData.has('is_admin')) data.is_admin = false;
    if (!formData.has('is_active')) data.is_active = false;
    if (!formData.has('email_notifications')) data.email_notifications = false;
    if (!formData.has('sms_notifications')) data.sms_notifications = false;
    
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    submitBtn.disabled = true;
    
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => {
                window.location.href = '{{ url_for("auth.users") }}';
            }, 1500);
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        showAlert('danger', 'حدث خطأ في الاتصال');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function showResetPasswordModal() {
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmPassword').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

function submitResetPassword() {
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (newPassword !== confirmPassword) {
        showAlert('danger', 'كلمات المرور غير متطابقة');
        return;
    }
    
    if (newPassword.length < 6) {
        showAlert('danger', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        return;
    }
    
    fetch(`/auth/users/{{ user.id }}/reset-password`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            new_password: newPassword
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        showAlert('danger', 'حدث خطأ في الاتصال');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
