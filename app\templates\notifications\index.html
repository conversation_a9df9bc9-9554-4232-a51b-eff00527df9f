{% extends "base.html" %}

{% block title %}إدارة الإشعارات - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-bell me-2"></i>إدارة الإشعارات</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">الإشعارات</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- إحصائيات الإشعارات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.total_sent_today }}</h4>
                            <p class="card-text">إشعارات اليوم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-paper-plane fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.successful_today }}</h4>
                            <p class="card-text">نجحت اليوم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.failed_today }}</h4>
                            <p class="card-text">فشلت اليوم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.pending_notifications }}</h4>
                            <p class="card-text">في الانتظار</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>إجراءات الإشعارات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <button type="button" class="btn btn-primary btn-lg w-100" onclick="runNotificationCheck()">
                                <i class="fas fa-sync me-2"></i>فحص الإشعارات الآن
                            </button>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button type="button" class="btn btn-info btn-lg w-100" onclick="showManualNotificationModal()">
                                <i class="fas fa-paper-plane me-2"></i>إرسال إشعار يدوي
                            </button>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="{{ url_for('notifications.settings') }}" class="btn btn-secondary btn-lg w-100">
                                <i class="fas fa-cog me-2"></i>إعدادات الإشعارات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الإشعارات الأخيرة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>الإشعارات الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_notifications %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المشترك</th>
                                        <th>رقم الصندوق</th>
                                        <th>نوع الإشعار</th>
                                        <th>الرسالة</th>
                                        <th>تاريخ الإرسال</th>
                                        <th>الحالة</th>
                                        <th>الأيام المتبقية</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for notification in recent_notifications %}
                                        <tr>
                                            <td>{{ notification.subscription.subscriber.name }}</td>
                                            <td>{{ notification.subscription.subscriber.box_number }}</td>
                                            <td>
                                                {% if notification.notification_type == 'email' %}
                                                    <span class="badge bg-primary">
                                                        <i class="fas fa-envelope me-1"></i>بريد إلكتروني
                                                    </span>
                                                {% elif notification.notification_type == 'sms' %}
                                                    <span class="badge bg-info">
                                                        <i class="fas fa-sms me-1"></i>رسالة نصية
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span title="{{ notification.message }}">
                                                    {{ notification.message[:50] }}{% if notification.message|length > 50 %}...{% endif %}
                                                </span>
                                            </td>
                                            <td>{{ notification.sent_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>
                                                {% if notification.is_successful %}
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>نجح
                                                    </span>
                                                {% else %}
                                                    <span class="badge bg-danger" title="{{ notification.error_message }}">
                                                        <i class="fas fa-times me-1"></i>فشل
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge bg-warning">{{ notification.days_before_expiry }} يوم</span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-bell fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد إشعارات</h5>
                            <p class="text-muted">لم يتم إرسال أي إشعارات بعد</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للإشعار اليدوي -->
<div class="modal fade" id="manualNotificationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال إشعار يدوي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="manualNotificationForm">
                    <div class="mb-3">
                        <label for="subscriberSelect" class="form-label">المشترك</label>
                        <select class="form-select" id="subscriberSelect" required>
                            <option value="">اختر المشترك...</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notificationType" class="form-label">نوع الإشعار</label>
                        <select class="form-select" id="notificationType" required>
                            <option value="email">بريد إلكتروني</option>
                            <option value="sms">رسالة نصية</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notificationMessage" class="form-label">الرسالة</label>
                        <textarea class="form-control" id="notificationMessage" rows="4" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="sendManualNotification()">إرسال الإشعار</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function runNotificationCheck() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الفحص...';
    btn.disabled = true;

    fetch('/notifications/run-check', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        showAlert('danger', 'حدث خطأ في الاتصال');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function showManualNotificationModal() {
    // Load subscribers
    fetch('/api/subscribers')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('subscriberSelect');
            select.innerHTML = '<option value="">اختر المشترك...</option>';
            data.forEach(subscriber => {
                select.innerHTML += `<option value="${subscriber.id}">${subscriber.name} - ${subscriber.box_number}</option>`;
            });
        });
    
    const modal = new bootstrap.Modal(document.getElementById('manualNotificationModal'));
    modal.show();
}

function sendManualNotification() {
    const form = document.getElementById('manualNotificationForm');
    const formData = new FormData(form);
    
    const data = {
        subscriber_id: document.getElementById('subscriberSelect').value,
        type: document.getElementById('notificationType').value,
        message: document.getElementById('notificationMessage').value
    };

    if (!data.subscriber_id || !data.message) {
        showAlert('warning', 'يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    fetch('/notifications/send-manual', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('manualNotificationModal')).hide();
            form.reset();
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        showAlert('danger', 'حدث خطأ في الاتصال');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
