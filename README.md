# نظام إدارة الصناديق البريدية (Postal Box Management System)

نظام شامل لإدارة الصناديق البريدية مع إمكانيات متقدمة للتتبع والإشعارات والتقارير.

## المميزات الرئيسية

### 🏢 إدارة المشتركين
- إضافة وتعديل وحذف بيانات المشتركين
- التحقق من تفرد رقم الصندوق ورقم الهوية/الجواز
- عرض قائمة المشتركين مع فلاتر متقدمة
- البحث السريع في بيانات المشتركين

### 📅 إدارة الاشتراكات
- تسجيل فترات الاشتراك (تاريخ البدء والنهاية)
- تتبع حالة الاشتراك (نشط، منتهي، متوقف)
- إمكانية تجديد الاشتراك
- عرض الاشتراكات المنتهية والتي ستنتهي قريباً

### 📊 التقارير والإحصائيات
- تقارير مالية شاملة (الإيرادات، المدفوعات)
- تقارير الاشتراكات المنتهية والمنتهية قريباً
- إحصائيات توزيع أحجام الصناديق
- رسوم بيانية تفاعلية

### 🔔 نظام الإشعارات
- إشعارات تلقائية قبل انتهاء الاشتراك (15، 7، 3، 1 يوم)
- إرسال إشعارات عبر البريد الإلكتروني و SMS
- سجل كامل للإشعارات المرسلة
- إمكانية إرسال إشعارات يدوية

### 📁 استيراد وتصدير البيانات
- استيراد البيانات من ملفات Excel
- تصدير التقارير إلى Excel و PDF
- قوالب جاهزة للاستيراد
- التحقق من صحة البيانات عند الاستيراد

### 🎨 واجهة مستخدم متجاوبة
- تصميم متجاوب يعمل على جميع الأجهزة
- واجهة باللغة العربية مع دعم RTL
- تصميم حديث باستخدام Bootstrap
- تفاعل سلس مع AJAX

## متطلبات النظام

- Python 3.8 أو أحدث
- Flask 2.3+
- SQLite (مدمج مع Python)
- متصفح ويب حديث

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd postal-box-management
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv

# على Windows
venv\Scripts\activate

# على Linux/Mac
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد متغيرات البيئة
انسخ ملف `.env` وقم بتعديل الإعدادات حسب احتياجاتك:

```bash
# Flask Configuration
FLASK_CONFIG=development
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=sqlite:///postal_box_management.db

# Email Configuration (للإشعارات)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

### 5. إنشاء قاعدة البيانات وإضافة بيانات تجريبية
```bash
python create_sample_data.py
```

### 6. تشغيل التطبيق
```bash
python run.py
```

الآن يمكنك الوصول للتطبيق على: `http://localhost:5000`

## هيكل المشروع

```
postal-box-management/
├── app/                          # مجلد التطبيق الرئيسي
│   ├── __init__.py              # إعداد التطبيق
│   ├── models.py                # نماذج قاعدة البيانات
│   ├── extensions.py            # إعدادات الإضافات
│   ├── notifications.py         # نظام الإشعارات
│   ├── main/                    # Blueprint الرئيسي
│   ├── subscribers/             # Blueprint المشتركين
│   ├── reports/                 # Blueprint التقارير
│   ├── import_export/           # Blueprint الاستيراد والتصدير
│   └── notifications_bp/        # Blueprint الإشعارات
├── templates/                   # قوالب HTML
│   ├── base.html               # القالب الأساسي
│   ├── dashboard.html          # لوحة التحكم
│   ├── subscribers/            # صفحات المشتركين
│   └── reports/                # صفحات التقارير
├── static/                     # الملفات الثابتة
│   ├── css/                    # ملفات CSS
│   └── js/                     # ملفات JavaScript
├── uploads/                    # مجلد الملفات المرفوعة
├── config.py                   # إعدادات التطبيق
├── requirements.txt            # متطلبات Python
├── run.py                      # ملف تشغيل التطبيق
├── create_sample_data.py       # إنشاء بيانات تجريبية
└── .env                        # متغيرات البيئة
```

## الاستخدام

### لوحة التحكم
- عرض إحصائيات سريعة عن النظام
- رسوم بيانية للاشتراكات الشهرية
- تنبيهات للاشتراكات المنتهية قريباً

### إدارة المشتركين
- **إضافة مشترك جديد**: من قائمة المشتركين > إضافة مشترك جديد
- **عرض التفاصيل**: انقر على أيقونة العين بجانب المشترك
- **تعديل البيانات**: انقر على أيقونة التعديل
- **البحث والفلترة**: استخدم مربع البحث والفلاتر في أعلى الصفحة

### التقارير
- **التقارير المالية**: عرض الإيرادات والمدفوعات
- **الاشتراكات المنتهية**: قائمة بالاشتراكات المنتهية
- **التصدير**: تصدير التقارير إلى Excel

### الاستيراد من Excel
- انقر على "استيراد من Excel" في القائمة الجانبية
- حمل قالب Excel أو استخدم ملفك الخاص
- تأكد من وجود الأعمدة المطلوبة

## إعداد الإشعارات

### البريد الإلكتروني
1. قم بإنشاء كلمة مرور تطبيق في Gmail
2. أضف بيانات البريد في ملف `.env`
3. فعل الإشعارات من إعدادات النظام

### SMS (اختياري)
1. أنشئ حساب في Twilio
2. أضف بيانات Twilio في ملف `.env`
3. فعل إشعارات SMS

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- افتح issue في GitHub
- راسلنا على البريد الإلكتروني

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بمساهماتكم! يرجى قراءة دليل المساهمة قبل إرسال pull request.

---

تم تطوير هذا النظام باستخدام:
- **Backend**: Python, Flask, SQLAlchemy
- **Frontend**: HTML, CSS, JavaScript, Bootstrap
- **Database**: SQLite
- **Charts**: Chart.js
- **Excel**: Pandas, OpenPyXL
