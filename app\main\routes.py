# -*- coding: utf-8 -*-
"""
Main routes for the application
"""

from flask import render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required
from app.main import bp
from app.models import Subscriber, Subscription, Notification
from app.extensions import db
from datetime import date, timedelta
from sqlalchemy import func

@bp.route('/')
@login_required
def index():
    """Dashboard - Main page"""
    # Get statistics for dashboard
    total_subscribers = Subscriber.query.filter_by(is_active=True).count()
    
    # Active subscriptions (not expired)
    active_subscriptions = Subscription.query.join(Subscriber).filter(
        Subscription.end_date >= date.today(),
        Subscription.is_active == True,
        Subscriber.is_active == True
    ).count()
    
    # Expired subscriptions
    expired_subscriptions = Subscription.query.join(Subscriber).filter(
        Subscription.end_date < date.today(),
        Subscription.is_active == True,
        Subscriber.is_active == True
    ).count()
    
    # Expiring soon (within 15 days)
    expiring_soon = Subscription.query.join(Subscriber).filter(
        Subscription.end_date <= date.today() + timedelta(days=15),
        Subscription.end_date >= date.today(),
        Subscription.is_active == True,
        Subscriber.is_active == True
    ).count()
    
    # Recent subscriptions (last 30 days)
    recent_subscriptions = Subscription.query.join(Subscriber).filter(
        Subscription.created_at >= date.today() - timedelta(days=30),
        Subscriber.is_active == True
    ).count()
    
    # Box size distribution
    box_sizes = db.session.query(
        Subscriber.box_size,
        func.count(Subscriber.id).label('count')
    ).filter_by(is_active=True).group_by(Subscriber.box_size).all()

    # Convert box sizes to Arabic
    size_map = {
        'Small': 'صغير',
        'Medium': 'متوسط',
        'Large': 'كبير'
    }
    box_sizes_arabic = {size_map.get(size, size): count for size, count in box_sizes}

    stats = {
        'total_subscribers': total_subscribers,
        'active_subscriptions': active_subscriptions,
        'expired_subscriptions': expired_subscriptions,
        'expiring_soon': expiring_soon,
        'recent_subscriptions': recent_subscriptions,
        'box_sizes': box_sizes_arabic
    }
    
    return render_template('dashboard.html', stats=stats)

@bp.route('/search')
@login_required
def search():
    """Search functionality"""
    query = request.args.get('q', '').strip()
    search_type = request.args.get('type', 'all')
    
    if not query:
        return jsonify({'results': []})
    
    results = []
    
    if search_type in ['all', 'subscribers']:
        # Search subscribers
        subscribers = Subscriber.query.filter(
            db.or_(
                Subscriber.name.contains(query),
                Subscriber.box_number.contains(query),
                Subscriber.passport_number.contains(query),
                Subscriber.phone.contains(query)
            ),
            Subscriber.is_active == True
        ).limit(10).all()
        
        for subscriber in subscribers:
            current_sub = subscriber.current_subscription
            # Convert status to Arabic
            status_map = {
                'Active': 'نشط',
                'Expired': 'منتهي',
                'Expiring Soon': 'ينتهي قريباً',
                'غير مشترك': 'لا يوجد اشتراك'
            }
            status_arabic = status_map.get(subscriber.subscription_status, subscriber.subscription_status)

            results.append({
                'type': 'subscriber',
                'id': subscriber.id,
                'name': subscriber.name,
                'box_number': subscriber.box_number,
                'phone': subscriber.phone,
                'status': status_arabic,
                'url': url_for('subscribers.view', id=subscriber.id)
            })
    
    return jsonify({'results': results})

@bp.route('/api/subscribers')
@login_required
def api_subscribers():
    """API endpoint for getting subscribers list"""
    subscribers = Subscriber.query.filter_by(is_active=True).all()
    return jsonify([{
        'id': s.id,
        'name': s.name,
        'box_number': s.box_number,
        'email': s.email,
        'phone': s.phone
    } for s in subscribers])

@bp.route('/api/dashboard-data')
@login_required
def dashboard_data():
    """API endpoint for dashboard data (for AJAX updates)"""
    # Get monthly subscription data for charts
    monthly_data = db.session.query(
        func.strftime('%Y-%m', Subscription.created_at).label('month'),
        func.count(Subscription.id).label('count')
    ).filter(
        Subscription.created_at >= date.today() - timedelta(days=365)
    ).group_by(func.strftime('%Y-%m', Subscription.created_at)).all()
    
    # Get expiring subscriptions for alerts
    expiring_subscriptions = Subscription.query.join(Subscriber).filter(
        Subscription.end_date <= date.today() + timedelta(days=15),
        Subscription.end_date >= date.today(),
        Subscription.is_active == True,
        Subscriber.is_active == True
    ).all()
    
    expiring_list = []
    for sub in expiring_subscriptions:
        expiring_list.append({
            'subscriber_name': sub.subscriber.name,
            'box_number': sub.subscriber.box_number,
            'end_date': sub.end_date.strftime('%Y-%m-%d'),
            'days_remaining': sub.days_remaining,
            'phone': sub.subscriber.phone
        })
    
    return jsonify({
        'monthly_subscriptions': [{'month': m[0], 'count': m[1]} for m in monthly_data],
        'expiring_subscriptions': expiring_list
    })
