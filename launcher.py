#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الصناديق البريدية - مشغل مستقل مع واجهة مرئية
"""

import os
import sys
import webbrowser
import threading
import time
import tkinter as tk
from tkinter import ttk, messagebox
import subprocess

# إعداد المسارات
if getattr(sys, 'frozen', False):
    base_path = os.path.dirname(sys.executable)
    app_path = sys._MEIPASS
else:
    base_path = os.path.dirname(os.path.abspath(__file__))
    app_path = base_path

os.chdir(base_path)
sys.path.insert(0, app_path)

class PostalBoxLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام إدارة الصناديق البريدية")
        self.root.geometry("500x400")
        self.root.resizable(False, False)

        # تعيين الأيقونة إذا كانت موجودة
        try:
            if os.path.exists('icon.ico'):
                self.root.iconbitmap('icon.ico')
        except:
            pass

        # متغيرات
        self.server_running = False
        self.app = None
        self.server_thread = None

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)

        title_label = tk.Label(
            title_frame,
            text="🚀 نظام إدارة الصناديق البريدية",
            font=('Arial', 16, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(expand=True)

        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)

        # معلومات النظام
        info_frame = tk.LabelFrame(main_frame, text="معلومات النظام", font=('Arial', 10, 'bold'))
        info_frame.pack(fill='x', pady=(0, 15))

        info_text = """
🌐 عنوان النظام: http://localhost:5000
👤 اسم المستخدم: admin
🔑 كلمة المرور: admin123
        """

        info_label = tk.Label(info_frame, text=info_text, justify='right', font=('Arial', 9))
        info_label.pack(pady=10)

        # حالة النظام
        self.status_frame = tk.LabelFrame(main_frame, text="حالة النظام", font=('Arial', 10, 'bold'))
        self.status_frame.pack(fill='x', pady=(0, 15))

        self.status_label = tk.Label(
            self.status_frame,
            text="⏸️ النظام متوقف",
            font=('Arial', 10),
            fg='red'
        )
        self.status_label.pack(pady=10)

        # شريط التقدم
        self.progress = ttk.Progressbar(
            self.status_frame,
            mode='indeterminate',
            length=300
        )
        self.progress.pack(pady=5)

        # الأزرار
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(0, 15))

        self.start_button = tk.Button(
            button_frame,
            text="🚀 تشغيل النظام",
            command=self.start_system,
            bg='#27ae60',
            fg='white',
            font=('Arial', 12, 'bold'),
            height=2,
            width=15
        )
        self.start_button.pack(side='left', padx=(0, 10))

        self.stop_button = tk.Button(
            button_frame,
            text="🛑 إيقاف النظام",
            command=self.stop_system,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 12, 'bold'),
            height=2,
            width=15,
            state='disabled'
        )
        self.stop_button.pack(side='left', padx=(0, 10))

        self.browser_button = tk.Button(
            button_frame,
            text="🌐 فتح المتصفح",
            command=self.open_browser,
            bg='#3498db',
            fg='white',
            font=('Arial', 12, 'bold'),
            height=2,
            width=15,
            state='disabled'
        )
        self.browser_button.pack(side='left')

        # رسائل النظام
        log_frame = tk.LabelFrame(main_frame, text="رسائل النظام", font=('Arial', 10, 'bold'))
        log_frame.pack(fill='both', expand=True)

        # إنشاء Text widget مع scrollbar
        text_frame = tk.Frame(log_frame)
        text_frame.pack(fill='both', expand=True, padx=5, pady=5)

        self.log_text = tk.Text(
            text_frame,
            height=8,
            wrap='word',
            font=('Consolas', 9),
            bg='#f8f9fa',
            fg='#2c3e50'
        )

        scrollbar = tk.Scrollbar(text_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # رسالة ترحيب
        self.log_message("مرحباً بك في نظام إدارة الصناديق البريدية")
        self.log_message("اضغط 'تشغيل النظام' للبدء")

    def log_message(self, message):
        """إضافة رسالة إلى سجل الرسائل"""
        timestamp = time.strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.log_text.insert('end', full_message)
        self.log_text.see('end')
        self.root.update_idletasks()

    def start_system(self):
        """تشغيل النظام"""
        try:
            self.log_message("🔄 بدء تشغيل النظام...")
            self.progress.start()

            # تعطيل زر التشغيل
            self.start_button.config(state='disabled')

            # تحديث الحالة
            self.status_label.config(text="⏳ جاري التشغيل...", fg='orange')

            # بدء الخادم في خيط منفصل
            self.server_thread = threading.Thread(target=self._run_server, daemon=True)
            self.server_thread.start()

        except Exception as e:
            self.log_message(f"❌ خطأ في التشغيل: {e}")
            messagebox.showerror("خطأ", f"فشل في تشغيل النظام:\n{e}")
            self._reset_ui()

    def _run_server(self):
        """تشغيل الخادم في خيط منفصل"""
        try:
            # إعداد المجلدات
            os.makedirs('instance', exist_ok=True)
            os.makedirs('instance/uploads', exist_ok=True)

            self.log_message("📁 تم إنشاء المجلدات المطلوبة")

            # استيراد التطبيق
            from app import create_app
            from app.extensions import db

            self.log_message("📦 تم تحميل التطبيق")

            self.app = create_app('production')

            with self.app.app_context():
                db.create_all()
                self.log_message("🗄️ تم إعداد قاعدة البيانات")

                # إنشاء مستخدم افتراضي
                from app.models import User
                if not User.query.filter_by(username='admin').first():
                    admin = User(username='admin', email='<EMAIL>', is_admin=True)
                    admin.set_password('admin123')
                    db.session.add(admin)
                    db.session.commit()
                    self.log_message("👤 تم إنشاء المستخدم الافتراضي")

            # تحديث واجهة المستخدم
            self.root.after(0, self._server_started)

            # تشغيل الخادم
            self.app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)

        except Exception as e:
            self.root.after(0, lambda: self._server_error(str(e)))

    def _server_started(self):
        """تحديث الواجهة عند بدء الخادم"""
        self.server_running = True
        self.progress.stop()

        self.status_label.config(text="✅ النظام يعمل", fg='green')
        self.stop_button.config(state='normal')
        self.browser_button.config(state='normal')

        self.log_message("✅ تم تشغيل النظام بنجاح")
        self.log_message("🌐 النظام متاح على: http://localhost:5000")

        # فتح المتصفح تلقائياً
        threading.Thread(target=self._delayed_browser_open, daemon=True).start()

    def _delayed_browser_open(self):
        """فتح المتصفح بعد تأخير قصير"""
        time.sleep(2)
        self.open_browser()

    def _server_error(self, error):
        """معالجة خطأ الخادم"""
        self.log_message(f"❌ خطأ في الخادم: {error}")
        messagebox.showerror("خطأ", f"فشل في تشغيل الخادم:\n{error}")
        self._reset_ui()

    def stop_system(self):
        """إيقاف النظام"""
        try:
            self.log_message("🛑 جاري إيقاف النظام...")
            self.server_running = False

            # إيقاف الخادم (سيتم إيقافه عند إغلاق التطبيق)
            self._reset_ui()
            self.log_message("✅ تم إيقاف النظام")

        except Exception as e:
            self.log_message(f"❌ خطأ في الإيقاف: {e}")

    def _reset_ui(self):
        """إعادة تعيين واجهة المستخدم"""
        self.progress.stop()
        self.status_label.config(text="⏸️ النظام متوقف", fg='red')
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.browser_button.config(state='disabled')

    def open_browser(self):
        """فتح المتصفح"""
        try:
            webbrowser.open('http://localhost:5000')
            self.log_message("🌐 تم فتح المتصفح")
        except Exception as e:
            self.log_message(f"⚠️ لم يتم فتح المتصفح: {e}")
            messagebox.showwarning("تحذير", "لم يتم فتح المتصفح تلقائياً\nافتح المتصفح يدوياً على: http://localhost:5000")

    def on_closing(self):
        """معالجة إغلاق النافذة"""
        if self.server_running:
            if messagebox.askokcancel("إغلاق", "هل تريد إغلاق النظام؟"):
                self.stop_system()
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        launcher = PostalBoxLauncher()
        launcher.run()
    except Exception as e:
        # في حالة فشل الواجهة المرئية، استخدم النسخة النصية
        print(f"❌ خطأ في الواجهة المرئية: {e}")
        print("🔄 التبديل إلى الوضع النصي...")
        fallback_main()

def fallback_main():
    """نسخة احتياطية نصية"""
    print("🚀 نظام إدارة الصناديق البريدية")
    print("=" * 50)

    try:
        # إعداد المجلدات
        os.makedirs('instance', exist_ok=True)
        os.makedirs('instance/uploads', exist_ok=True)

        # استيراد التطبيق
        from app import create_app
        from app.extensions import db

        app = create_app('production')

        with app.app_context():
            db.create_all()

            # إنشاء مستخدم افتراضي
            from app.models import User
            if not User.query.filter_by(username='admin').first():
                admin = User(username='admin', email='<EMAIL>', is_admin=True)
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()

        print("✅ النظام جاهز")
        print("🌐 العنوان: http://localhost:5000")
        print("👤 المستخدم: admin | كلمة المرور: admin123")
        print("=" * 50)

        # فتح المتصفح
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open('http://localhost:5000')
                print("🌐 تم فتح المتصفح")
            except:
                print("🌐 افتح المتصفح على: http://localhost:5000")

        threading.Thread(target=open_browser, daemon=True).start()

        # تشغيل الخادم
        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)

    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
