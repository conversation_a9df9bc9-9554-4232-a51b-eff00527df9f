{% extends "base.html" %}

{% block title %}الملف الشخصي - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-circle me-2"></i>الملف الشخصي</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">الملف الشخصي</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- User Info Card -->
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-id-card me-2"></i>معلومات المستخدم
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-user-circle fa-5x text-primary"></i>
                    </div>
                    <h5 class="card-title">{{ current_user.full_name }}</h5>
                    <p class="card-text text-muted">@{{ current_user.username }}</p>
                    <p class="card-text">
                        <i class="fas fa-envelope me-2"></i>{{ current_user.email }}
                    </p>
                    {% if current_user.is_admin %}
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-crown me-1"></i>مدير النظام
                        </span>
                    {% endif %}
                    
                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">تاريخ الإنشاء</small>
                            <div>{{ current_user.created_at.strftime('%Y-%m-%d') }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">آخر دخول</small>
                            <div>
                                {% if current_user.last_login %}
                                    {{ current_user.last_login.strftime('%Y-%m-%d %H:%M') }}
                                {% else %}
                                    لم يسجل دخول من قبل
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification Settings Card -->
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell me-2"></i>إعدادات الإشعارات
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form id="notificationForm" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">
                                    <i class="fas fa-cog me-2"></i>أنواع الإشعارات
                                </h6>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="email_notifications" 
                                           name="email_notifications" {{ 'checked' if current_user.email_notifications }}>
                                    <label class="form-check-label" for="email_notifications">
                                        <i class="fas fa-envelope me-2 text-primary"></i>إشعارات البريد الإلكتروني
                                    </label>
                                    <div class="form-text">استقبال إشعارات انتهاء الاشتراكات عبر البريد الإلكتروني</div>
                                </div>

                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="sms_notifications" 
                                           name="sms_notifications" {{ 'checked' if current_user.sms_notifications }}>
                                    <label class="form-check-label" for="sms_notifications">
                                        <i class="fas fa-sms me-2 text-success"></i>إشعارات الرسائل النصية
                                    </label>
                                    <div class="form-text">استقبال إشعارات انتهاء الاشتراكات عبر الرسائل النصية</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h6 class="text-primary">
                                    <i class="fas fa-clock me-2"></i>توقيت الإشعارات
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="notification_days_before" class="form-label">
                                        <i class="fas fa-calendar-alt me-2"></i>إرسال الإشعار قبل انتهاء الاشتراك بـ
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="notification_days_before" 
                                               name="notification_days_before" value="{{ current_user.notification_days_before }}" 
                                               min="1" max="30" required>
                                        <span class="input-group-text">يوم</span>
                                    </div>
                                    <div class="form-text">يمكن تحديد من 1 إلى 30 يوم</div>
                                </div>

                                <div class="alert alert-light">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>معلومات مفيدة
                                    </h6>
                                    <ul class="mb-0">
                                        <li>سيتم إرسال إشعارات تلقائية في الأوقات التالية: 15، 7، 3، 1 يوم قبل انتهاء الاشتراك</li>
                                        <li>يمكنك تخصيص التوقيت الرئيسي للإشعارات حسب احتياجاتك</li>
                                        <li>الإشعارات تساعد في متابعة الاشتراكات وتجديدها في الوقت المناسب</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-info">
                                <span class="normal-text">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </span>
                                <span class="loading d-none">
                                    <i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Recent Activity Card -->
            <div class="card shadow mt-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>النشاط الأخير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تسجيل الدخول</h6>
                                <p class="timeline-text">
                                    {% if current_user.last_login %}
                                        {{ current_user.last_login.strftime('%Y-%m-%d في %H:%M') }}
                                    {% else %}
                                        هذا هو أول تسجيل دخول لك
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">إنشاء الحساب</h6>
                                <p class="timeline-text">{{ current_user.created_at.strftime('%Y-%m-%d في %H:%M') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    color: #495057;
}

.timeline-text {
    margin-bottom: 0;
    color: #6c757d;
    font-size: 0.9rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('notificationForm');

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = form.querySelector('button[type="submit"]');
        const normalText = submitBtn.querySelector('.normal-text');
        const loadingText = submitBtn.querySelector('.loading');

        // Show loading state
        normalText.classList.add('d-none');
        loadingText.classList.remove('d-none');
        submitBtn.disabled = true;

        // Prepare form data
        const formData = new FormData(form);
        const data = {
            email_notifications: formData.get('email_notifications') === 'on',
            sms_notifications: formData.get('sms_notifications') === 'on',
            notification_days_before: parseInt(formData.get('notification_days_before'))
        };

        // Send AJAX request
        fetch(form.action || window.location.pathname, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message || 'تم حفظ الإعدادات بنجاح');
            } else {
                showAlert('danger', data.error || 'حدث خطأ غير متوقع');
            }
            resetButton();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'حدث خطأ في الاتصال');
            resetButton();
        });

        function resetButton() {
            normalText.classList.remove('d-none');
            loadingText.classList.add('d-none');
            submitBtn.disabled = false;
        }
    });

    function showAlert(type, message) {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.alert:not(.alert-light)');
        existingAlerts.forEach(alert => alert.remove());

        // Create new alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert alert at the top of the card body
        const cardBody = document.querySelector('.card-body');
        cardBody.insertBefore(alertDiv, cardBody.firstChild);
    }
});
</script>
{% endblock %}
