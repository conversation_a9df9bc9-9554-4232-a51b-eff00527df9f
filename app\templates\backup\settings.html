{% extends "base.html" %}

{% block title %}إعدادات النسخ الاحتياطي - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cog me-2"></i>إعدادات النسخ الاحتياطي</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('backup.index') }}">النسخ الاحتياطي</a></li>
                        <li class="breadcrumb-item active">الإعدادات</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h me-2"></i>إعدادات النسخ الاحتياطي
                    </h5>
                </div>
                <div class="card-body">
                    <form id="settingsForm" method="POST">
                        <!-- إعدادات عامة -->
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-cog me-2"></i>الإعدادات العامة
                        </h6>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="max_backups" class="form-label">
                                    <i class="fas fa-list-ol me-2"></i>عدد النسخ الاحتياطية المحفوظة
                                </label>
                                <input type="number" class="form-control" id="max_backups" name="max_backups" 
                                       value="{{ config.get('max_backups', 10) }}" min="1" max="100" required>
                                <div class="form-text">عدد النسخ الاحتياطية التي سيتم الاحتفاظ بها (سيتم حذف الأقدم تلقائياً)</div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="backup_interval_hours" class="form-label">
                                    <i class="fas fa-clock me-2"></i>فترة النسخ التلقائي (بالساعات)
                                </label>
                                <input type="number" class="form-control" id="backup_interval_hours" name="backup_interval_hours" 
                                       value="{{ config.get('backup_interval_hours', 24) }}" min="1" max="168" required>
                                <div class="form-text">كم ساعة بين كل نسخة احتياطية تلقائية</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="backup_dir" class="form-label">
                                <i class="fas fa-folder me-2"></i>مجلد النسخ الاحتياطي
                            </label>
                            <input type="text" class="form-control" id="backup_dir" name="backup_dir" 
                                   value="{{ config.get('backup_dir', 'backups') }}" required>
                            <div class="form-text">المجلد الذي سيتم حفظ النسخ الاحتياطية فيه</div>
                        </div>

                        <hr>

                        <!-- إعدادات النسخ -->
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-database me-2"></i>إعدادات النسخ
                        </h6>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="auto_backup_enabled" 
                                           name="auto_backup_enabled" {{ 'checked' if config.get('auto_backup_enabled', True) }}>
                                    <label class="form-check-label" for="auto_backup_enabled">
                                        <i class="fas fa-robot me-2"></i>تفعيل النسخ التلقائي
                                    </label>
                                    <div class="form-text">إنشاء نسخ احتياطية تلقائياً حسب الفترة المحددة</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="compress_backup" 
                                           name="compress_backup" {{ 'checked' if config.get('compress_backup', True) }}>
                                    <label class="form-check-label" for="compress_backup">
                                        <i class="fas fa-file-archive me-2"></i>ضغط النسخ الاحتياطية
                                    </label>
                                    <div class="form-text">ضغط النسخ الاحتياطية لتوفير مساحة التخزين</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="include_uploads" 
                                       name="include_uploads" {{ 'checked' if config.get('include_uploads', True) }}>
                                <label class="form-check-label" for="include_uploads">
                                    <i class="fas fa-file-upload me-2"></i>تضمين ملفات التحميل
                                </label>
                                <div class="form-text">تضمين ملفات التحميل والمرفقات في النسخة الاحتياطية</div>
                            </div>
                        </div>

                        <hr>

                        <!-- معلومات النظام -->
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i>معلومات النظام
                        </h6>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">آخر نسخة احتياطية</h6>
                                        <p class="card-text">
                                            {% if config.get('last_backup_time') %}
                                                {{ config.get('last_backup_time')[:19].replace('T', ' ') }}
                                            {% else %}
                                                لم يتم إنشاء نسخ احتياطية بعد
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">حالة النسخ التلقائي</h6>
                                        <p class="card-text">
                                            {% if config.get('auto_backup_enabled', True) %}
                                                <span class="text-success">
                                                    <i class="fas fa-check-circle me-1"></i>مفعل
                                                </span>
                                            {% else %}
                                                <span class="text-danger">
                                                    <i class="fas fa-times-circle me-1"></i>معطل
                                                </span>
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">حالة المجدول</h6>
                                        <p class="card-text" id="schedulerStatus">
                                            <span class="text-muted">
                                                <i class="fas fa-spinner fa-spin me-1"></i>جاري التحقق...
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h6 class="card-title">النسخة الاحتياطية التالية</h6>
                                        <p class="card-text" id="nextBackupTime">
                                            <i class="fas fa-clock me-1"></i>جاري التحقق...
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body">
                                        <h6 class="card-title">إجراءات سريعة</h6>
                                        <button type="button" class="btn btn-dark btn-sm me-2" onclick="triggerBackupNow()">
                                            <i class="fas fa-play me-1"></i>تشغيل النسخ الآن
                                        </button>
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="refreshSchedulerStatus()">
                                            <i class="fas fa-sync me-1"></i>تحديث الحالة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- أزرار الحفظ -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('backup.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <span class="normal-text">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </span>
                                <span class="loading d-none">
                                    <i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- بطاقة تحذيرات -->
            <div class="card shadow mt-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>تحذيرات مهمة
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li class="mb-2">
                            <strong>النسخ التلقائي:</strong> تأكد من أن الخادم يعمل باستمرار لضمان عمل النسخ التلقائي
                        </li>
                        <li class="mb-2">
                            <strong>مساحة التخزين:</strong> تأكد من وجود مساحة كافية في مجلد النسخ الاحتياطي
                        </li>
                        <li class="mb-2">
                            <strong>الأمان:</strong> احتفظ بنسخ احتياطية في مواقع متعددة لضمان الأمان
                        </li>
                        <li class="mb-0">
                            <strong>الاستعادة:</strong> اختبر عملية الاستعادة بانتظام للتأكد من سلامة النسخ
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('settingsForm');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = form.querySelector('button[type="submit"]');
        const normalText = submitBtn.querySelector('.normal-text');
        const loadingText = submitBtn.querySelector('.loading');

        // Show loading state
        normalText.classList.add('d-none');
        loadingText.classList.remove('d-none');
        submitBtn.disabled = true;

        // Prepare form data
        const formData = new FormData(form);
        const data = {
            max_backups: formData.get('max_backups'),
            backup_interval_hours: formData.get('backup_interval_hours'),
            backup_dir: formData.get('backup_dir'),
            auto_backup_enabled: formData.get('auto_backup_enabled') === 'on',
            compress_backup: formData.get('compress_backup') === 'on',
            include_uploads: formData.get('include_uploads') === 'on'
        };

        // Send AJAX request
        fetch(form.action || window.location.pathname, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message || 'تم حفظ الإعدادات بنجاح');
            } else {
                showAlert('danger', data.error || 'حدث خطأ غير متوقع');
            }
            resetButton();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'حدث خطأ في الاتصال');
            resetButton();
        });

        function resetButton() {
            normalText.classList.remove('d-none');
            loadingText.classList.add('d-none');
            submitBtn.disabled = false;
        }
    });

    function showAlert(type, message) {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create new alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert alert at the top of the container
        const container = document.querySelector('.container-fluid');
        container.insertBefore(alertDiv, container.children[1]);
    }

    // تحديث معلومات الجدولة عند تحميل الصفحة
    refreshSchedulerStatus();
});

function refreshSchedulerStatus() {
    fetch('{{ url_for("backup.api_scheduler_status") }}')
        .then(response => response.json())
        .then(data => {
            const statusElement = document.getElementById('schedulerStatus');
            const nextBackupElement = document.getElementById('nextBackupTime');

            if (data.error) {
                statusElement.innerHTML = `
                    <span class="text-danger">
                        <i class="fas fa-times-circle me-1"></i>خطأ: ${data.error}
                    </span>
                `;
                nextBackupElement.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>غير متاح';
            } else {
                // حالة المجدول
                if (data.running && data.job_scheduled) {
                    statusElement.innerHTML = `
                        <span class="text-success">
                            <i class="fas fa-check-circle me-1"></i>يعمل بشكل طبيعي
                        </span>
                    `;
                } else if (data.running && !data.job_scheduled) {
                    statusElement.innerHTML = `
                        <span class="text-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>يعمل بدون جدولة
                        </span>
                    `;
                } else {
                    statusElement.innerHTML = `
                        <span class="text-danger">
                            <i class="fas fa-times-circle me-1"></i>متوقف
                        </span>
                    `;
                }

                // موعد النسخة التالية
                if (data.next_run_time) {
                    const nextRun = new Date(data.next_run_time);
                    nextBackupElement.innerHTML = `
                        <i class="fas fa-clock me-1"></i>${nextRun.toLocaleString('ar-EG')}
                    `;
                } else {
                    nextBackupElement.innerHTML = '<i class="fas fa-minus me-1"></i>غير مجدول';
                }
            }
        })
        .catch(error => {
            console.error('Error fetching scheduler status:', error);
            document.getElementById('schedulerStatus').innerHTML = `
                <span class="text-danger">
                    <i class="fas fa-times-circle me-1"></i>خطأ في الاتصال
                </span>
            `;
        });
}

function triggerBackupNow() {
    if (!confirm('هل تريد تشغيل النسخ الاحتياطي الآن؟')) {
        return;
    }

    fetch('{{ url_for("backup.api_trigger_backup") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || 'تم تشغيل النسخ الاحتياطي');
        } else {
            showAlert('danger', data.error || 'فشل في تشغيل النسخ الاحتياطي');
        }
    })
    .catch(error => {
        console.error('Error triggering backup:', error);
        showAlert('danger', 'حدث خطأ في الاتصال');
    });
}
</script>
{% endblock %}
