# نظام إدارة الصناديق البريدية - النسخة المستقلة الكاملة

## 🎉 مرحباً بك في نظام إدارة الصناديق البريدية

هذا نظام شامل لإدارة الصناديق البريدية والاشتراكات، مصمم ليعمل على أي جهاز كمبيوتر بدون الحاجة لتثبيت أي برامج إضافية.

## 🚀 طريقة التشغيل

### الطريقة الأولى (الأسهل):
1. انقر نقراً مزدوجاً على `تشغيل_النظام.bat`
2. ستظهر نافذة أوامر مع تعليمات واضحة
3. انتظر حتى يبدأ النظام (قد يستغرق دقيقة في المرة الأولى)
4. سيفتح المتصفح تلقائياً

### الطريقة الثانية:
1. انقر نقراً مزدوجاً على `PostalBoxManager.exe`
2. اتبع التعليمات في نافذة الأوامر

## 👤 بيانات تسجيل الدخول

- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 🌐 عناوين الوصول

- **العنوان الرئيسي**: http://localhost:5000
- **العنوان البديل**: http://127.0.0.1:5000

## 📋 مميزات النظام

### إدارة المشتركين
- إضافة وتعديل وحذف المشتركين
- تتبع معلومات الاتصال والعناوين
- إدارة أنواع الصناديق المختلفة

### إدارة الاشتراكات
- إنشاء اشتراكات جديدة
- تجديد الاشتراكات المنتهية
- حساب الرسوم والمدفوعات
- تتبع تواريخ الانتهاء

### التقارير والإحصائيات
- تقارير مالية مفصلة
- إحصائيات الاشتراكات
- تقارير المشتركين النشطين
- تصدير التقارير إلى Excel

### نظام الإشعارات
- تنبيهات انتهاء الاشتراكات
- إشعارات المدفوعات المستحقة
- تذكيرات التجديد

### النسخ الاحتياطي
- نسخ احتياطي تلقائي يومي
- إمكانية إنشاء نسخ احتياطية يدوية
- استعادة البيانات من النسخ الاحتياطية

## 🔧 متطلبات النظام

- **نظام التشغيل**: Windows 10/11 (64-bit)
- **الذاكرة**: 512 MB RAM (الحد الأدنى)
- **المساحة**: 100 MB مساحة فارغة
- **الشبكة**: لا يحتاج اتصال بالإنترنت

## 📁 نقل النظام لجهاز آخر

يمكن نسخ هذا المجلد كاملاً لأي جهاز كمبيوتر آخر وسيعمل مباشرة بدون تثبيت.

## 🛑 إيقاف النظام

- اضغط `Ctrl+C` في نافذة الأوامر
- أو أغلق نافذة الأوامر مباشرة

## 🆘 استكشاف الأخطاء

### المشكلة: "المنفذ مستخدم"
**الحل**: تأكد من عدم تشغيل برنامج آخر على المنفذ 5000

### المشكلة: "خطأ في الصلاحيات"
**الحل**: شغل البرنامج كمدير (Run as Administrator)

### المشكلة: "المتصفح لا يفتح"
**الحل**: افتح المتصفح يدوياً واذهب إلى http://localhost:5000

### المشكلة: "النظام لا يبدأ"
**الحل**: 
1. تأكد من وجود جميع الملفات في نفس المجلد
2. شغل البرنامج كمدير
3. تأكد من عدم حجب برنامج مكافح الفيروسات للملف

## 📞 معلومات تقنية

- **إطار العمل**: Flask (Python)
- **قاعدة البيانات**: SQLite
- **حجم الملف**: ~55 MB
- **تاريخ البناء**: 2025-07-17 06:03:12

## 🔒 الأمان

- جميع البيانات محفوظة محلياً على جهازك
- لا يتم إرسال أي بيانات عبر الإنترنت
- كلمات المرور محمية بتشفير قوي

## 📈 التحديثات المستقبلية

- إضافة المزيد من أنواع التقارير
- تحسين واجهة المستخدم
- إضافة ميزات جديدة للإدارة

---

**شكراً لاستخدام نظام إدارة الصناديق البريدية!**

للدعم والاستفسارات، يرجى الاحتفاظ بهذا الملف للمراجعة.
