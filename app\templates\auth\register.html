{% extends "base.html" %}

{% block title %}إضافة مستخدم جديد - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">إضافة مستخدم</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>بيانات المستخدم الجديد
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form id="registerForm" method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>اسم المستخدم <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="username" name="username" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-2"></i>البريد الإلكتروني <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="full_name" class="form-label">
                                <i class="fas fa-id-card me-2"></i>الاسم الكامل <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>كلمة المرور <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required minlength="6">
                                <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>تأكيد كلمة المرور <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="6">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin">
                                <label class="form-check-label" for="is_admin">
                                    <i class="fas fa-crown me-2"></i>مدير النظام
                                </label>
                                <div class="form-text">المديرون يمكنهم إضافة مستخدمين جدد وإدارة النظام</div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <span class="normal-text">
                                    <i class="fas fa-save me-2"></i>إضافة المستخدم
                                </span>
                                <span class="loading d-none">
                                    <i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('registerForm');
    const usernameInput = document.getElementById('username');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');

    // Real-time validation
    usernameInput.addEventListener('blur', checkUsername);
    emailInput.addEventListener('blur', checkEmail);
    confirmPasswordInput.addEventListener('input', checkPasswordMatch);

    function checkUsername() {
        const username = usernameInput.value.trim();
        if (username.length < 3) {
            setFieldError(usernameInput, 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
            return;
        }

        fetch(`{{ url_for('auth.check_username') }}?username=${encodeURIComponent(username)}`)
            .then(response => response.json())
            .then(data => {
                if (data.exists) {
                    setFieldError(usernameInput, 'اسم المستخدم موجود مسبقاً');
                } else {
                    setFieldValid(usernameInput);
                }
            })
            .catch(error => {
                console.error('Error checking username:', error);
            });
    }

    function checkEmail() {
        const email = emailInput.value.trim();
        if (!email) return;

        fetch(`{{ url_for('auth.check_email') }}?email=${encodeURIComponent(email)}`)
            .then(response => response.json())
            .then(data => {
                if (data.exists) {
                    setFieldError(emailInput, 'البريد الإلكتروني موجود مسبقاً');
                } else {
                    setFieldValid(emailInput);
                }
            })
            .catch(error => {
                console.error('Error checking email:', error);
            });
    }

    function checkPasswordMatch() {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        if (confirmPassword && password !== confirmPassword) {
            setFieldError(confirmPasswordInput, 'كلمة المرور وتأكيدها غير متطابقتين');
        } else if (confirmPassword) {
            setFieldValid(confirmPasswordInput);
        }
    }

    function setFieldError(field, message) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        const feedback = field.nextElementSibling;
        if (feedback && feedback.classList.contains('invalid-feedback')) {
            feedback.textContent = message;
        }
    }

    function setFieldValid(field) {
        field.classList.add('is-valid');
        field.classList.remove('is-invalid');
    }

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = form.querySelector('button[type="submit"]');
        const normalText = submitBtn.querySelector('.normal-text');
        const loadingText = submitBtn.querySelector('.loading');

        // Show loading state
        normalText.classList.add('d-none');
        loadingText.classList.remove('d-none');
        submitBtn.disabled = true;

        // Prepare form data
        const formData = new FormData(form);
        const data = {
            username: formData.get('username'),
            email: formData.get('email'),
            full_name: formData.get('full_name'),
            password: formData.get('password'),
            confirm_password: formData.get('confirm_password'),
            is_admin: formData.get('is_admin') === 'on'
        };

        // Validate password match
        if (data.password !== data.confirm_password) {
            showAlert('danger', 'كلمة المرور وتأكيدها غير متطابقتين');
            resetButton();
            return;
        }

        // Send AJAX request
        fetch(form.action || window.location.pathname, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message || 'تم إضافة المستخدم بنجاح');
                form.reset();
                // Remove validation classes
                form.querySelectorAll('.is-valid, .is-invalid').forEach(el => {
                    el.classList.remove('is-valid', 'is-invalid');
                });
            } else {
                showAlert('danger', data.error || 'حدث خطأ غير متوقع');
            }
            resetButton();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'حدث خطأ في الاتصال');
            resetButton();
        });

        function resetButton() {
            normalText.classList.remove('d-none');
            loadingText.classList.add('d-none');
            submitBtn.disabled = false;
        }
    });

    function showAlert(type, message) {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create new alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert alert at the top of the card body
        const cardBody = document.querySelector('.card-body');
        cardBody.insertBefore(alertDiv, cardBody.firstChild);
    }
});
</script>
{% endblock %}
