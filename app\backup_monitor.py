# -*- coding: utf-8 -*-
"""
نظام مراقبة النسخ الاحتياطي
"""

import os
import json
import logging
from datetime import datetime, timedelta
from app.backup import backup_system
from app.scheduler import backup_scheduler

logger = logging.getLogger('backup_monitor')

class BackupMonitor:
    """نظام مراقبة النسخ الاحتياطي"""
    
    def __init__(self):
        """تهيئة نظام المراقبة"""
        self.monitor_file = 'backup_monitor.json'
        self.alerts = []
        self.load_monitor_data()
    
    def load_monitor_data(self):
        """تحميل بيانات المراقبة"""
        try:
            if os.path.exists(self.monitor_file):
                with open(self.monitor_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.alerts = data.get('alerts', [])
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات المراقبة: {str(e)}")
            self.alerts = []
    
    def save_monitor_data(self):
        """حفظ بيانات المراقبة"""
        try:
            data = {
                'alerts': self.alerts,
                'last_check': datetime.now().isoformat()
            }
            with open(self.monitor_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات المراقبة: {str(e)}")
    
    def add_alert(self, alert_type, message, severity='info'):
        """إضافة تنبيه جديد"""
        alert = {
            'id': len(self.alerts) + 1,
            'type': alert_type,
            'message': message,
            'severity': severity,
            'timestamp': datetime.now().isoformat(),
            'resolved': False
        }
        self.alerts.append(alert)
        self.save_monitor_data()
        logger.info(f"تم إضافة تنبيه جديد: {message}")
    
    def resolve_alert(self, alert_id):
        """حل تنبيه"""
        for alert in self.alerts:
            if alert['id'] == alert_id:
                alert['resolved'] = True
                alert['resolved_at'] = datetime.now().isoformat()
                self.save_monitor_data()
                return True
        return False
    
    def get_active_alerts(self):
        """الحصول على التنبيهات النشطة"""
        return [alert for alert in self.alerts if not alert.get('resolved', False)]
    
    def get_all_alerts(self, limit=50):
        """الحصول على جميع التنبيهات"""
        # ترتيب التنبيهات حسب التاريخ (الأحدث أولاً)
        sorted_alerts = sorted(self.alerts, key=lambda x: x['timestamp'], reverse=True)
        return sorted_alerts[:limit]
    
    def check_backup_health(self):
        """فحص صحة النسخ الاحتياطي"""
        health_report = {
            'status': 'healthy',
            'issues': [],
            'warnings': [],
            'info': []
        }
        
        try:
            # فحص نظام النسخ الاحتياطي
            if not backup_system:
                health_report['status'] = 'critical'
                health_report['issues'].append('نظام النسخ الاحتياطي غير متاح')
                self.add_alert('system_error', 'نظام النسخ الاحتياطي غير متاح', 'critical')
                return health_report
            
            # فحص الإعدادات
            config = backup_system.config
            
            # فحص مجلد النسخ الاحتياطي
            backup_dir = config.get('backup_dir', 'backups')
            if not os.path.exists(backup_dir):
                health_report['status'] = 'warning'
                health_report['warnings'].append(f'مجلد النسخ الاحتياطي غير موجود: {backup_dir}')
                self.add_alert('directory_missing', f'مجلد النسخ الاحتياطي غير موجود: {backup_dir}', 'warning')
            
            # فحص مساحة التخزين
            if os.path.exists(backup_dir):
                try:
                    import shutil
                    total, used, free = shutil.disk_usage(backup_dir)
                    free_gb = free / (1024**3)
                    
                    if free_gb < 1:  # أقل من 1 GB
                        health_report['status'] = 'critical'
                        health_report['issues'].append(f'مساحة التخزين منخفضة جداً: {free_gb:.2f} GB')
                        self.add_alert('low_disk_space', f'مساحة التخزين منخفضة جداً: {free_gb:.2f} GB', 'critical')
                    elif free_gb < 5:  # أقل من 5 GB
                        health_report['status'] = 'warning'
                        health_report['warnings'].append(f'مساحة التخزين منخفضة: {free_gb:.2f} GB')
                        self.add_alert('low_disk_space', f'مساحة التخزين منخفضة: {free_gb:.2f} GB', 'warning')
                    
                except Exception as e:
                    health_report['warnings'].append(f'لا يمكن فحص مساحة التخزين: {str(e)}')
            
            # فحص آخر نسخة احتياطية
            last_backup_time = config.get('last_backup_time')
            if last_backup_time:
                try:
                    last_backup = datetime.fromisoformat(last_backup_time)
                    backup_interval = timedelta(hours=config.get('backup_interval_hours', 24))
                    time_since_backup = datetime.now() - last_backup
                    
                    if time_since_backup > backup_interval * 2:  # أكثر من ضعف الفترة المحددة
                        health_report['status'] = 'warning'
                        health_report['warnings'].append(f'لم يتم إنشاء نسخة احتياطية منذ {time_since_backup.days} أيام')
                        self.add_alert('backup_overdue', f'لم يتم إنشاء نسخة احتياطية منذ {time_since_backup.days} أيام', 'warning')
                    
                except Exception as e:
                    health_report['warnings'].append(f'خطأ في فحص آخر نسخة احتياطية: {str(e)}')
            else:
                health_report['warnings'].append('لم يتم إنشاء أي نسخة احتياطية بعد')
                self.add_alert('no_backups', 'لم يتم إنشاء أي نسخة احتياطية بعد', 'warning')
            
            # فحص النسخ الاحتياطية الموجودة
            backups = backup_system.list_backups()
            if len(backups) == 0:
                health_report['warnings'].append('لا توجد نسخ احتياطية')
                self.add_alert('no_backups', 'لا توجد نسخ احتياطية', 'warning')
            elif len(backups) < 3:
                health_report['info'].append(f'عدد النسخ الاحتياطية قليل: {len(backups)}')
            
            # فحص نظام الجدولة
            if backup_scheduler:
                scheduler_status = backup_scheduler.get_scheduler_status()
                
                if not scheduler_status.get('running', False):
                    health_report['status'] = 'warning'
                    health_report['warnings'].append('نظام الجدولة متوقف')
                    self.add_alert('scheduler_stopped', 'نظام الجدولة متوقف', 'warning')
                
                if config.get('auto_backup_enabled', True) and not scheduler_status.get('job_scheduled', False):
                    health_report['status'] = 'warning'
                    health_report['warnings'].append('النسخ التلقائي مفعل لكن غير مجدول')
                    self.add_alert('auto_backup_not_scheduled', 'النسخ التلقائي مفعل لكن غير مجدول', 'warning')
            else:
                health_report['status'] = 'warning'
                health_report['warnings'].append('نظام الجدولة غير متاح')
                self.add_alert('scheduler_unavailable', 'نظام الجدولة غير متاح', 'warning')
            
            # فحص قاعدة البيانات
            db_path = backup_system.get_db_path()
            if not db_path or not os.path.exists(db_path):
                health_report['status'] = 'critical'
                health_report['issues'].append('ملف قاعدة البيانات غير موجود')
                self.add_alert('database_missing', 'ملف قاعدة البيانات غير موجود', 'critical')
            
        except Exception as e:
            health_report['status'] = 'critical'
            health_report['issues'].append(f'خطأ في فحص صحة النظام: {str(e)}')
            self.add_alert('health_check_error', f'خطأ في فحص صحة النظام: {str(e)}', 'critical')
            logger.error(f"خطأ في فحص صحة النسخ الاحتياطي: {str(e)}", exc_info=True)
        
        return health_report
    
    def generate_backup_report(self):
        """إنشاء تقرير النسخ الاحتياطي"""
        try:
            report = {
                'generated_at': datetime.now().isoformat(),
                'health_status': self.check_backup_health(),
                'backup_statistics': {},
                'scheduler_status': {},
                'alerts_summary': {}
            }
            
            # إحصائيات النسخ الاحتياطي
            if backup_system:
                backups = backup_system.list_backups()
                config = backup_system.config
                
                total_size = sum(backup.get('size', 0) for backup in backups)
                
                report['backup_statistics'] = {
                    'total_backups': len(backups),
                    'total_size_bytes': total_size,
                    'total_size_mb': round(total_size / (1024 * 1024), 2),
                    'oldest_backup': backups[-1]['timestamp'] if backups else None,
                    'newest_backup': backups[0]['timestamp'] if backups else None,
                    'last_backup_time': config.get('last_backup_time'),
                    'auto_backup_enabled': config.get('auto_backup_enabled', True),
                    'backup_interval_hours': config.get('backup_interval_hours', 24),
                    'max_backups': config.get('max_backups', 10)
                }
            
            # حالة المجدول
            if backup_scheduler:
                report['scheduler_status'] = backup_scheduler.get_scheduler_status()
            
            # ملخص التنبيهات
            active_alerts = self.get_active_alerts()
            all_alerts = self.get_all_alerts(10)
            
            report['alerts_summary'] = {
                'active_alerts_count': len(active_alerts),
                'total_alerts_count': len(self.alerts),
                'recent_alerts': all_alerts[:5],
                'critical_alerts': [alert for alert in active_alerts if alert.get('severity') == 'critical'],
                'warning_alerts': [alert for alert in active_alerts if alert.get('severity') == 'warning']
            }
            
            return report
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير النسخ الاحتياطي: {str(e)}")
            return {
                'generated_at': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def send_health_report_to_admins(self):
        """إرسال تقرير الصحة للمديرين"""
        try:
            from app.models import User
            from app.notifications import notification_service
            
            if not notification_service:
                logger.warning("نظام الإشعارات غير متاح")
                return False
            
            # إنشاء التقرير
            report = self.generate_backup_report()
            health_status = report.get('health_status', {})
            
            # إرسال التقرير فقط إذا كانت هناك مشاكل
            if health_status.get('status') in ['warning', 'critical']:
                # الحصول على المديرين النشطين
                admins = User.query.filter_by(is_admin=True, is_active=True).all()
                
                subject = f"تقرير صحة النسخ الاحتياطي - {health_status.get('status', 'غير معروف')}"
                
                # إنشاء رسالة التقرير
                message_parts = [
                    f"تقرير صحة النسخ الاحتياطي - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    "",
                    f"الحالة العامة: {health_status.get('status', 'غير معروف')}",
                    ""
                ]
                
                # إضافة المشاكل الحرجة
                issues = health_status.get('issues', [])
                if issues:
                    message_parts.append("المشاكل الحرجة:")
                    for issue in issues:
                        message_parts.append(f"- {issue}")
                    message_parts.append("")
                
                # إضافة التحذيرات
                warnings = health_status.get('warnings', [])
                if warnings:
                    message_parts.append("التحذيرات:")
                    for warning in warnings:
                        message_parts.append(f"- {warning}")
                    message_parts.append("")
                
                # إضافة إحصائيات النسخ الاحتياطي
                stats = report.get('backup_statistics', {})
                if stats:
                    message_parts.extend([
                        "إحصائيات النسخ الاحتياطي:",
                        f"- عدد النسخ: {stats.get('total_backups', 0)}",
                        f"- الحجم الإجمالي: {stats.get('total_size_mb', 0)} MB",
                        f"- آخر نسخة: {stats.get('last_backup_time', 'غير محدد')}",
                        ""
                    ])
                
                message_parts.append("نظام إدارة الصناديق البريدية")
                message = "\n".join(message_parts)
                
                # إرسال التقرير للمديرين
                sent_count = 0
                for admin in admins:
                    if admin.email_notifications and admin.email:
                        if notification_service.send_email_notification(admin.email, subject, message, None):
                            sent_count += 1
                
                logger.info(f"تم إرسال تقرير الصحة إلى {sent_count} مدير")
                return sent_count > 0
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إرسال تقرير الصحة: {str(e)}")
            return False
    
    def cleanup_old_alerts(self, days=30):
        """تنظيف التنبيهات القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # الاحتفاظ بالتنبيهات الحديثة أو غير المحلولة
            self.alerts = [
                alert for alert in self.alerts
                if (not alert.get('resolved', False) or 
                    datetime.fromisoformat(alert['timestamp']) > cutoff_date)
            ]
            
            self.save_monitor_data()
            logger.info(f"تم تنظيف التنبيهات الأقدم من {days} يوم")
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف التنبيهات القديمة: {str(e)}")


# تهيئة نظام المراقبة
backup_monitor = BackupMonitor()
