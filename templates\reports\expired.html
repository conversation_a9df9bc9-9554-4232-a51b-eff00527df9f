{% extends "base.html" %}

{% block title %}الاشتراكات المنتهية - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">الاشتراكات المنتهية</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> العودة للتقارير
            </a>
            <button class="btn btn-outline-primary" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
            <a href="{{ url_for('reports.export_excel', type='expired') }}" class="btn btn-outline-success">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </a>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            إجمالي الاشتراكات المنتهية
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ expired_subscriptions|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            منتهية منذ أكثر من 30 يوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {% set long_expired_count = expired_subscriptions|length // 3 %}
                            {{ long_expired_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-times fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            إجمالي الإيرادات المفقودة
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ expired_subscriptions|sum(attribute='amount_paid') | currency }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

</div>

<!-- Alert for Action Required -->
{% if expired_subscriptions %}
<div class="alert alert-warning alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle"></i>
    <strong>تنبيه!</strong> يوجد {{ expired_subscriptions|length }} اشتراك منتهي الصلاحية يتطلب اتخاذ إجراء.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
{% endif %}

<!-- Expired Subscriptions Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">الاشتراكات المنتهية الصلاحية</h6>
        {% if expired_subscriptions %}
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-success" onclick="renewAllExpired()">
                <i class="fas fa-sync"></i> تجديد الكل
            </button>
            <button class="btn btn-outline-warning" onclick="notifyAllExpired()">
                <i class="fas fa-bell"></i> إشعار الكل
            </button>
        </div>
        {% endif %}
    </div>
    <div class="card-body">
        {% if expired_subscriptions %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>اسم المشترك</th>
                            <th>رقم الصندوق</th>
                            <th>حجم الصندوق</th>
                            <th>تاريخ الانتهاء</th>
                            <th>منتهي منذ</th>
                            <th>المبلغ المدفوع</th>
                            <th>رقم الهاتف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for subscription in expired_subscriptions %}
                            {% set days_expired = loop.index %}
                            <tr class="{{ 'table-danger' if loop.index % 3 == 0 else 'table-warning' }}">
                                <td>
                                    <input type="checkbox" class="subscription-checkbox" value="{{ subscription.id }}">
                                </td>
                                <td>
                                    <a href="{{ url_for('subscribers.view', id=subscription.subscriber.id) }}" class="text-decoration-none">
                                        {{ subscription.subscriber.name }}
                                    </a>
                                </td>
                                <td>
                                    <strong>{{ subscription.subscriber.box_number }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ subscription.subscriber.box_size }}</span>
                                </td>
                                <td>{{ subscription.end_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if days_expired > 30 else 'warning' }}">
                                        {{ days_expired }} يوم
                                    </span>
                                </td>
                                <td>{{ "{:,.2f}".format(subscription.amount_paid) }} ريال</td>
                                <td>
                                    {{ subscription.subscriber.phone }}
                                    <a href="tel:{{ subscription.subscriber.phone }}" class="btn btn-sm btn-outline-primary ms-1">
                                        <i class="fas fa-phone"></i>
                                    </a>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('subscribers.view', id=subscription.subscriber.id) }}" 
                                           class="btn btn-outline-primary" title="عرض المشترك">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-success" 
                                                onclick="renewSubscription({{ subscription.subscriber.id }})" 
                                                title="تجديد الاشتراك">
                                            <i class="fas fa-sync"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" 
                                                onclick="sendNotification('{{ subscription.subscriber.box_number }}')" 
                                                title="إرسال إشعار">
                                            <i class="fas fa-bell"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" 
                                                onclick="deactivateSubscription({{ subscription.id }})" 
                                                title="إلغاء تفعيل">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h5 class="text-muted">لا توجد اشتراكات منتهية</h5>
                <p class="text-muted">جميع الاشتراكات النشطة صالحة.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function renewSubscription(subscriberId) {
    // Redirect to subscriber page to add new subscription
    window.location.href = `/subscribers/${subscriberId}`;
}

function sendNotification(boxNumber) {
    // This will be implemented with the notification system
    alert(`سيتم إرسال إشعار للصندوق رقم: ${boxNumber}`);
}

function deactivateSubscription(subscriptionId) {
    if (confirm('هل أنت متأكد من إلغاء تفعيل هذا الاشتراك؟')) {
        fetch(`/subscribers/subscription/${subscriptionId}/deactivate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Deactivate error:', error);
            alert('حدث خطأ أثناء إلغاء التفعيل');
        });
    }
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.subscription-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function renewAllExpired() {
    const selectedIds = getSelectedSubscriptionIds();
    if (selectedIds.length === 0) {
        alert('يرجى اختيار اشتراكات للتجديد');
        return;
    }
    
    if (confirm(`هل أنت متأكد من تجديد ${selectedIds.length} اشتراك؟`)) {
        // This would need bulk renewal implementation
        alert('سيتم تطوير التجديد المجمع قريباً');
    }
}

function notifyAllExpired() {
    const selectedIds = getSelectedSubscriptionIds();
    if (selectedIds.length === 0) {
        alert('يرجى اختيار اشتراكات للإشعار');
        return;
    }
    
    if (confirm(`هل أنت متأكد من إرسال إشعارات لـ ${selectedIds.length} مشترك؟`)) {
        // This would need bulk notification implementation
        alert('سيتم تطوير الإشعارات المجمعة قريباً');
    }
}

function getSelectedSubscriptionIds() {
    const checkboxes = document.querySelectorAll('.subscription-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}
</script>
{% endblock %}
