{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">لوحة التحكم</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt"></i> تحديث
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي المشتركين
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.total_subscribers }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            الاشتراكات النشطة
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.active_subscriptions }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            ستنتهي قريباً
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.expiring_soon }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            منتهية الصلاحية
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.expired_subscriptions }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Monthly Subscriptions Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">الاشتراكات الشهرية</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="monthlyChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Box Size Distribution -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">توزيع أحجام الصناديق</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="boxSizeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Expiring Subscriptions Alert -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">الاشتراكات التي ستنتهي قريباً</h6>
            </div>
            <div class="card-body">
                <div id="expiringSubscriptions">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('subscribers.add') }}" class="btn btn-primary btn-block">
                            <i class="fas fa-user-plus"></i> إضافة مشترك جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('reports.expiring') }}" class="btn btn-warning btn-block">
                            <i class="fas fa-clock"></i> الاشتراكات المنتهية
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('reports.financial') }}" class="btn btn-success btn-block">
                            <i class="fas fa-chart-line"></i> التقارير المالية
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-info btn-block" onclick="showImportModal()">
                            <i class="fas fa-file-excel"></i> استيراد من Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Dashboard data
let dashboardData = {};

// Load dashboard data
function loadDashboardData() {
    fetch('/api/dashboard-data')
        .then(response => response.json())
        .then(data => {
            dashboardData = data;
            updateCharts();
            updateExpiringSubscriptions();
        })
        .catch(error => console.error('Dashboard data error:', error));
}

// Update charts
function updateCharts() {
    // Monthly subscriptions chart
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: dashboardData.monthly_subscriptions ? dashboardData.monthly_subscriptions.map(item => item.month) : [],
            datasets: [{
                label: 'عدد الاشتراكات',
                data: dashboardData.monthly_subscriptions ? dashboardData.monthly_subscriptions.map(item => item.count) : [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'الاشتراكات الشهرية'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Box size distribution chart
    const boxSizeCtx = document.getElementById('boxSizeChart').getContext('2d');
    const boxSizes = {{ stats.box_sizes | tojson }};
    
    new Chart(boxSizeCtx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(boxSizes),
            datasets: [{
                data: Object.values(boxSizes),
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'توزيع أحجام الصناديق'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Update expiring subscriptions
function updateExpiringSubscriptions() {
    const container = document.getElementById('expiringSubscriptions');
    
    if (!dashboardData.expiring_subscriptions || dashboardData.expiring_subscriptions.length === 0) {
        container.innerHTML = '<div class="alert alert-success">لا توجد اشتراكات ستنتهي قريباً</div>';
        return;
    }
    
    const html = `
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>اسم المشترك</th>
                        <th>رقم الصندوق</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الأيام المتبقية</th>
                        <th>رقم الهاتف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${dashboardData.expiring_subscriptions.map(sub => `
                        <tr>
                            <td>${sub.subscriber_name}</td>
                            <td>${sub.box_number}</td>
                            <td>${sub.end_date}</td>
                            <td>
                                <span class="badge ${sub.days_remaining <= 7 ? 'bg-danger' : 'bg-warning'}">
                                    ${sub.days_remaining} يوم
                                </span>
                            </td>
                            <td>${sub.phone}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="callSubscriber('${sub.phone}')">
                                    <i class="fas fa-phone"></i>
                                </button>
                                <button class="btn btn-sm btn-success" onclick="sendNotification('${sub.box_number}')">
                                    <i class="fas fa-envelope"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = html;
}

// Helper functions
function callSubscriber(phone) {
    window.open(`tel:${phone}`);
}

function sendNotification(boxNumber) {
    // This will be implemented with the notification system
    alert(`سيتم إرسال إشعار للصندوق رقم: ${boxNumber}`);
}

function refreshDashboard() {
    location.reload();
}

// Load data on page load
document.addEventListener('DOMContentLoaded', loadDashboardData);
</script>
{% endblock %}
