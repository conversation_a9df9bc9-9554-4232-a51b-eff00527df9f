# -*- coding: utf-8 -*-
"""
Database models for Postal Box Management System
"""

from datetime import datetime, date, timedelta
from app.extensions import db
from sqlalchemy import Index
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin

class Subscriber(db.Model):
    """Model for postal box subscribers"""
    __tablename__ = 'subscribers'
    
    id = db.Column(db.Integer, primary_key=True)
    box_number = db.Column(db.String(20), unique=True, nullable=False, index=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    address = db.Column(db.Text, nullable=True)
    passport_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    box_size = db.Column(db.String(20), nullable=False)  # Small, Medium, Large
    email = db.Column(db.String(120), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    # Relationship with subscriptions
    subscriptions = db.relationship('Subscription', backref='subscriber', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Subscriber {self.name} - Box {self.box_number}>'
    
    @property
    def current_subscription(self):
        """Get the current active subscription"""
        return self.subscriptions.filter_by(is_active=True).first()
    
    @property
    def subscription_status(self):
        """Get current subscription status"""
        current_sub = self.current_subscription
        if not current_sub:
            return 'غير مشترك'

        today = date.today()
        if current_sub.end_date < today:
            return 'Expired'
        elif current_sub.end_date <= today + timedelta(days=15):
            return 'Expiring Soon'
        else:
            return 'Active'

    @property
    def box_size_arabic(self):
        """Get box size in Arabic"""
        size_map = {
            'Small': 'صغير',
            'Medium': 'متوسط',
            'Large': 'كبير'
        }
        return size_map.get(self.box_size, self.box_size)

    @property
    def subscription_status_arabic(self):
        """Get subscription status in Arabic"""
        status = self.subscription_status
        status_map = {
            'Active': 'نشط',
            'Expired': 'منتهي',
            'Expiring Soon': 'ينتهي قريباً',
            'غير مشترك': 'لا يوجد اشتراك'
        }
        return status_map.get(status, status)


class User(UserMixin, db.Model):
    """Model for system users (administrators)"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)

    # Notification preferences
    email_notifications = db.Column(db.Boolean, default=True)
    sms_notifications = db.Column(db.Boolean, default=False)
    notification_days_before = db.Column(db.Integer, default=15)  # Days before expiry to send notifications

    def __repr__(self):
        return f'<User {self.username}>'

    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)

    def update_last_login(self):
        """Update last login timestamp"""
        self.last_login = datetime.utcnow()
        db.session.commit()


class Subscription(db.Model):
    """Model for subscription periods"""
    __tablename__ = 'subscriptions'
    
    id = db.Column(db.Integer, primary_key=True)
    subscriber_id = db.Column(db.Integer, db.ForeignKey('subscribers.id'), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    amount_paid = db.Column(db.Float, nullable=False, default=0.0)
    payment_date = db.Column(db.Date, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship with notifications
    notifications = db.relationship('Notification', backref='subscription', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Subscription {self.subscriber.name} - {self.start_date} to {self.end_date}>'
    
    @property
    def days_remaining(self):
        """Calculate days remaining until expiry"""
        today = date.today()
        if self.end_date < today:
            return 0
        return (self.end_date - today).days
    
    @property
    def is_expired(self):
        """Check if subscription is expired"""
        return self.end_date < date.today()
    
    @property
    def is_expiring_soon(self):
        """Check if subscription is expiring within 15 days"""
        from datetime import timedelta
        return self.end_date <= date.today() + timedelta(days=15)

class Notification(db.Model):
    """Model for tracking sent notifications"""
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), nullable=False)
    notification_type = db.Column(db.String(20), nullable=False)  # email, sms, whatsapp
    message = db.Column(db.Text, nullable=False)
    sent_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_successful = db.Column(db.Boolean, default=False)
    error_message = db.Column(db.Text, nullable=True)
    days_before_expiry = db.Column(db.Integer, nullable=False)
    
    def __repr__(self):
        return f'<Notification {self.notification_type} - {self.subscription.subscriber.name}>'

# Create indexes for better performance
Index('idx_subscriber_box_number', Subscriber.box_number)
Index('idx_subscriber_passport', Subscriber.passport_number)
Index('idx_subscription_dates', Subscription.start_date, Subscription.end_date)
Index('idx_subscription_active', Subscription.is_active)
Index('idx_user_username', User.username)
Index('idx_user_email', User.email)
Index('idx_user_active', User.is_active)
