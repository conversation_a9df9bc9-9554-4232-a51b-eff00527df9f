# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي لتطبيق إدارة الصناديق البريدية
"""

import os
import sys
import time
import json
import shutil
import sqlite3
import logging
import zipfile
import datetime
import subprocess
from pathlib import Path
import psutil

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('backup.log')
    ]
)
logger = logging.getLogger('backup_system')

# الإعدادات الافتراضية للنسخ الاحتياطي
DEFAULT_BACKUP_CONFIG = {
    'backup_dir': 'backups',
    'max_backups': 10,
    'backup_interval_hours': 24,
    'include_uploads': True,
    'compress_backup': True,
    'auto_backup_enabled': True,
    'last_backup_time': None,
    'backup_name_format': 'backup_%Y%m%d_%H%M%S'
}


class BackupSystem:
    """نظام النسخ الاحتياطي"""
    
    def __init__(self, app=None, config=None):
        """تهيئة نظام النسخ الاحتياطي"""
        self.app = app
        self.config = config or DEFAULT_BACKUP_CONFIG.copy()
        self.backup_dir = self.config.get('backup_dir', 'backups')
        
        # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
            logger.info(f"تم إنشاء مجلد النسخ الاحتياطي: {self.backup_dir}")
        
        # تحميل الإعدادات من الملف إذا كان موجوداً
        self.config_file = os.path.join(self.backup_dir, 'backup_config.json')
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
                    logger.info("تم تحميل إعدادات النسخ الاحتياطي")
            except Exception as e:
                logger.error(f"خطأ في تحميل إعدادات النسخ الاحتياطي: {str(e)}")
        
        # حفظ الإعدادات
        self.save_config()
    
    def save_config(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            logger.info("تم حفظ إعدادات النسخ الاحتياطي")
            return True
        except Exception as e:
            logger.error(f"خطأ في حفظ إعدادات النسخ الاحتياطي: {str(e)}")
            return False
    
    def update_config(self, new_config):
        """تحديث إعدادات النسخ الاحتياطي"""
        self.config.update(new_config)
        
        # إذا تم تغيير مجلد النسخ الاحتياطي، قم بإنشائه
        if 'backup_dir' in new_config and new_config['backup_dir'] != self.backup_dir:
            self.backup_dir = new_config['backup_dir']
            if not os.path.exists(self.backup_dir):
                os.makedirs(self.backup_dir)
                logger.info(f"تم إنشاء مجلد النسخ الاحتياطي الجديد: {self.backup_dir}")
            
            # تحديث مسار ملف الإعدادات
            self.config_file = os.path.join(self.backup_dir, 'backup_config.json')
        
        return self.save_config()
    
    def create_backup(self, backup_name=None):
        """إنشاء نسخة احتياطية جديدة"""
        try:
            # إنشاء اسم النسخة الاحتياطية
            timestamp = datetime.datetime.now()
            if not backup_name:
                backup_name = timestamp.strftime(self.config.get('backup_name_format', 'backup_%Y%m%d_%H%M%S'))
            
            # إنشاء مجلد مؤقت للنسخة الاحتياطية
            temp_dir = os.path.join(self.backup_dir, f"temp_{int(time.time())}")
            os.makedirs(temp_dir, exist_ok=True)
            
            # نسخ قاعدة البيانات
            db_path = self.get_db_path()
            if db_path and os.path.exists(db_path):
                # إنشاء نسخة من قاعدة البيانات
                db_backup_path = os.path.join(temp_dir, os.path.basename(db_path))
                self.backup_database(db_path, db_backup_path)
                logger.info(f"تم نسخ قاعدة البيانات إلى: {db_backup_path}")
            else:
                logger.warning("لم يتم العثور على ملف قاعدة البيانات")
            
            # نسخ ملفات التحميل إذا كان مطلوباً
            if self.config.get('include_uploads', True):
                uploads_dir = self.get_uploads_dir()
                if uploads_dir and os.path.exists(uploads_dir):
                    uploads_backup_dir = os.path.join(temp_dir, 'uploads')
                    shutil.copytree(uploads_dir, uploads_backup_dir)
                    logger.info(f"تم نسخ ملفات التحميل إلى: {uploads_backup_dir}")
            
            # نسخ ملف الإعدادات
            config_backup_path = os.path.join(temp_dir, 'app_config.json')
            self.backup_config_files(config_backup_path)
            
            # إنشاء ملف معلومات النسخة الاحتياطية
            backup_info = {
                'name': backup_name,
                'timestamp': timestamp.isoformat(),
                'db_file': os.path.basename(db_path) if db_path else None,
                'includes_uploads': self.config.get('include_uploads', True),
                'system_info': self.get_system_info()
            }
            
            with open(os.path.join(temp_dir, 'backup_info.json'), 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=4)
            
            # ضغط النسخة الاحتياطية إذا كان مطلوباً
            backup_path = os.path.join(self.backup_dir, f"{backup_name}")
            if self.config.get('compress_backup', True):
                backup_path += '.zip'
                self.compress_directory(temp_dir, backup_path)
                logger.info(f"تم ضغط النسخة الاحتياطية إلى: {backup_path}")
            else:
                # نقل الملفات من المجلد المؤقت إلى مجلد النسخة الاحتياطية
                shutil.move(temp_dir, backup_path)
                logger.info(f"تم إنشاء النسخة الاحتياطية في: {backup_path}")
            
            # حذف المجلد المؤقت إذا كان لا يزال موجوداً
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            
            # تحديث وقت آخر نسخة احتياطية
            self.config['last_backup_time'] = timestamp.isoformat()
            self.save_config()
            
            # التحقق من عدد النسخ الاحتياطية وحذف القديمة إذا تجاوز الحد
            self.cleanup_old_backups()
            
            return {
                'success': True,
                'backup_path': backup_path,
                'backup_info': backup_info
            }
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}", exc_info=True)
            # حذف المجلد المؤقت في حالة الخطأ
            if 'temp_dir' in locals() and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            
            return {
                'success': False,
                'error': str(e)
            }
    
    def backup_database(self, source_db, target_db):
        """نسخ قاعدة البيانات SQLite"""
        try:
            # استخدام نسخة احتياطية آمنة لقاعدة البيانات
            conn = sqlite3.connect(source_db)
            with open(target_db, 'wb') as f:
                for line in conn.iterdump():
                    f.write(f'{line}\n'.encode('utf-8'))
            conn.close()
            return True
        except Exception as e:
            logger.error(f"خطأ في نسخ قاعدة البيانات: {str(e)}")
            # نسخ الملف مباشرة إذا فشلت الطريقة الأولى
            try:
                shutil.copy2(source_db, target_db)
                return True
            except Exception as e2:
                logger.error(f"خطأ في النسخ المباشر لقاعدة البيانات: {str(e2)}")
                return False
    
    def backup_config_files(self, target_path):
        """نسخ ملفات الإعدادات"""
        try:
            config = {}
            
            # نسخ ملف .env إذا كان موجوداً
            env_path = '.env'
            if os.path.exists(env_path):
                with open(env_path, 'r', encoding='utf-8') as f:
                    env_content = f.read()
                config['env_file'] = env_content
            
            # نسخ ملفات الإعدادات الأخرى
            config_files = ['config.py', 'instance/config.py']
            for file_path in config_files:
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config[file_path] = f.read()
            
            # حفظ الإعدادات في ملف JSON
            with open(target_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            return True
        except Exception as e:
            logger.error(f"خطأ في نسخ ملفات الإعدادات: {str(e)}")
            return False
    
    def compress_directory(self, source_dir, target_zip):
        """ضغط مجلد إلى ملف ZIP"""
        try:
            with zipfile.ZipFile(target_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, _, files in os.walk(source_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, source_dir)
                        zipf.write(file_path, arcname)
            return True
        except Exception as e:
            logger.error(f"خطأ في ضغط المجلد: {str(e)}")
            return False
    
    def get_db_path(self):
        """الحصول على مسار قاعدة البيانات"""
        # محاولة الحصول على مسار قاعدة البيانات من التطبيق
        if self.app and hasattr(self.app, 'config'):
            db_uri = self.app.config.get('SQLALCHEMY_DATABASE_URI')
            if db_uri and db_uri.startswith('sqlite:///'):
                # استخراج مسار الملف من URI
                db_path = db_uri.replace('sqlite:///', '')
                if not os.path.isabs(db_path):
                    # تحويل المسار النسبي إلى مسار مطلق
                    db_path = os.path.abspath(db_path)
                return db_path
        
        # البحث عن ملفات قاعدة البيانات في المجلدات الشائعة
        common_paths = [
            'instance/postal_box.db',
            'postal_box.db',
            'app.db',
            'instance/app.db'
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                return os.path.abspath(path)
        
        return None
    
    def get_uploads_dir(self):
        """الحصول على مجلد ملفات التحميل"""
        # محاولة الحصول على مسار مجلد التحميل من التطبيق
        if self.app and hasattr(self.app, 'config'):
            upload_folder = self.app.config.get('UPLOAD_FOLDER')
            if upload_folder and os.path.exists(upload_folder):
                return upload_folder
        
        # البحث عن مجلدات التحميل الشائعة
        common_paths = [
            'uploads',
            'app/static/uploads',
            'static/uploads',
            'instance/uploads'
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                return os.path.abspath(path)
        
        return None
    
    def get_system_info(self):
        """الحصول على معلومات النظام"""
        try:
            info = {
                'platform': sys.platform,
                'python_version': sys.version,
                'hostname': os.uname().nodename if hasattr(os, 'uname') else 'unknown',
                'cpu_count': psutil.cpu_count(),
                'memory': {
                    'total': psutil.virtual_memory().total,
                    'available': psutil.virtual_memory().available
                },
                'disk': {
                    'total': psutil.disk_usage('/').total,
                    'free': psutil.disk_usage('/').free
                }
            }
            return info
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات النظام: {str(e)}")
            return {'error': str(e)}
    
    def list_backups(self):
        """الحصول على قائمة النسخ الاحتياطية"""
        backups = []
        
        try:
            # البحث عن ملفات النسخ الاحتياطية
            for item in os.listdir(self.backup_dir):
                item_path = os.path.join(self.backup_dir, item)
                
                # تخطي المجلدات المؤقتة والملفات غير ذات الصلة
                if item.startswith('temp_') or item == 'backup_config.json':
                    continue
                
                backup_info = None
                
                # استخراج معلومات النسخة الاحتياطية
                if item.endswith('.zip') and os.path.isfile(item_path):
                    # استخراج معلومات من ملف ZIP
                    try:
                        with zipfile.ZipFile(item_path, 'r') as zipf:
                            if 'backup_info.json' in zipf.namelist():
                                with zipf.open('backup_info.json') as f:
                                    backup_info = json.loads(f.read().decode('utf-8'))
                    except Exception as e:
                        logger.error(f"خطأ في قراءة معلومات النسخة الاحتياطية من الملف المضغوط {item}: {str(e)}")
                
                elif os.path.isdir(item_path):
                    # استخراج معلومات من المجلد
                    info_path = os.path.join(item_path, 'backup_info.json')
                    if os.path.exists(info_path):
                        try:
                            with open(info_path, 'r', encoding='utf-8') as f:
                                backup_info = json.load(f)
                        except Exception as e:
                            logger.error(f"خطأ في قراءة معلومات النسخة الاحتياطية من المجلد {item}: {str(e)}")
                
                # إذا لم نتمكن من استخراج المعلومات، نستخدم البيانات الأساسية
                if not backup_info:
                    stat = os.stat(item_path)
                    backup_info = {
                        'name': item.replace('.zip', ''),
                        'timestamp': datetime.datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        'size': stat.st_size,
                        'path': item_path
                    }
                else:
                    # إضافة معلومات إضافية
                    backup_info['path'] = item_path
                    backup_info['size'] = os.path.getsize(item_path)
                
                backups.append(backup_info)
            
            # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
            backups.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            
            return backups
        
        except Exception as e:
            logger.error(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {str(e)}")
            return []
    
    def restore_backup(self, backup_path):
        """استعادة نسخة احتياطية"""
        try:
            # التحقق من وجود النسخة الاحتياطية
            if not os.path.exists(backup_path):
                return {
                    'success': False,
                    'error': f"النسخة الاحتياطية غير موجودة: {backup_path}"
                }
            
            # إنشاء مجلد مؤقت لاستخراج النسخة الاحتياطية
            temp_dir = os.path.join(self.backup_dir, f"restore_temp_{int(time.time())}")
            os.makedirs(temp_dir, exist_ok=True)
            
            # استخراج النسخة الاحتياطية إذا كانت مضغوطة
            if backup_path.endswith('.zip'):
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
            else:
                # نسخ محتويات المجلد إلى المجلد المؤقت
                for item in os.listdir(backup_path):
                    s = os.path.join(backup_path, item)
                    d = os.path.join(temp_dir, item)
                    if os.path.isdir(s):
                        shutil.copytree(s, d)
                    else:
                        shutil.copy2(s, d)
            
            # قراءة معلومات النسخة الاحتياطية
            info_path = os.path.join(temp_dir, 'backup_info.json')
            if os.path.exists(info_path):
                with open(info_path, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
            else:
                backup_info = {'name': os.path.basename(backup_path)}
            
            # استعادة قاعدة البيانات
            db_path = self.get_db_path()
            if db_path:
                # البحث عن ملف قاعدة البيانات في النسخة الاحتياطية
                db_backup_file = backup_info.get('db_file')
                if db_backup_file:
                    db_backup_path = os.path.join(temp_dir, db_backup_file)
                else:
                    # البحث عن أي ملف قاعدة بيانات
                    db_files = [f for f in os.listdir(temp_dir) if f.endswith('.db')]
                    if db_files:
                        db_backup_path = os.path.join(temp_dir, db_files[0])
                    else:
                        db_backup_path = None
                
                if db_backup_path and os.path.exists(db_backup_path):
                    # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
                    current_db_backup = f"{db_path}.bak.{int(time.time())}"
                    shutil.copy2(db_path, current_db_backup)
                    logger.info(f"تم إنشاء نسخة احتياطية من قاعدة البيانات الحالية: {current_db_backup}")
                    
                    # استعادة قاعدة البيانات
                    shutil.copy2(db_backup_path, db_path)
                    logger.info(f"تم استعادة قاعدة البيانات من: {db_backup_path}")
            
            # استعادة ملفات التحميل إذا كانت موجودة
            uploads_dir = self.get_uploads_dir()
            if uploads_dir and os.path.exists(os.path.join(temp_dir, 'uploads')):
                # إنشاء نسخة احتياطية من ملفات التحميل الحالية
                if os.path.exists(uploads_dir):
                    uploads_backup = f"{uploads_dir}.bak.{int(time.time())}"
                    shutil.copytree(uploads_dir, uploads_backup)
                    logger.info(f"تم إنشاء نسخة احتياطية من ملفات التحميل الحالية: {uploads_backup}")
                    
                    # حذف ملفات التحميل الحالية
                    shutil.rmtree(uploads_dir)
                
                # استعادة ملفات التحميل
                shutil.copytree(os.path.join(temp_dir, 'uploads'), uploads_dir)
                logger.info(f"تم استعادة ملفات التحميل من النسخة الاحتياطية")
            
            # حذف المجلد المؤقت
            shutil.rmtree(temp_dir)
            
            return {
                'success': True,
                'backup_info': backup_info,
                'message': "تم استعادة النسخة الاحتياطية بنجاح"
            }
            
        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}", exc_info=True)
            # حذف المجلد المؤقت في حالة الخطأ
            if 'temp_dir' in locals() and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            
            return {
                'success': False,
                'error': str(e)
            }
    
    def delete_backup(self, backup_path):
        """حذف نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                return {
                    'success': False,
                    'error': f"النسخة الاحتياطية غير موجودة: {backup_path}"
                }
            
            if os.path.isdir(backup_path):
                shutil.rmtree(backup_path)
            else:
                os.remove(backup_path)
            
            logger.info(f"تم حذف النسخة الاحتياطية: {backup_path}")
            
            return {
                'success': True,
                'message': "تم حذف النسخة الاحتياطية بنجاح"
            }
            
        except Exception as e:
            logger.error(f"خطأ في حذف النسخة الاحتياطية: {str(e)}")
            
            return {
                'success': False,
                'error': str(e)
            }
    
    def cleanup_old_backups(self):
        """حذف النسخ الاحتياطية القديمة إذا تجاوز عددها الحد المسموح"""
        try:
            max_backups = self.config.get('max_backups', 10)
            backups = self.list_backups()
            
            if len(backups) > max_backups:
                # حذف النسخ الاحتياطية القديمة
                for backup in backups[max_backups:]:
                    backup_path = backup.get('path')
                    if backup_path:
                        self.delete_backup(backup_path)
                        logger.info(f"تم حذف النسخة الاحتياطية القديمة: {backup_path}")
            
            return True
        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")
            return False
    
    def should_backup_now(self):
        """التحقق مما إذا كان يجب إنشاء نسخة احتياطية الآن"""
        if not self.config.get('auto_backup_enabled', True):
            return False
        
        last_backup_time = self.config.get('last_backup_time')
        if not last_backup_time:
            return True
        
        try:
            last_backup = datetime.datetime.fromisoformat(last_backup_time)
            backup_interval = datetime.timedelta(hours=self.config.get('backup_interval_hours', 24))
            now = datetime.datetime.now()
            
            return (now - last_backup) >= backup_interval
        except Exception as e:
            logger.error(f"خطأ في التحقق من وقت النسخ الاحتياطي: {str(e)}")
            return True


# تهيئة نظام النسخ الاحتياطي
backup_system = None

def init_app(app):
    """تهيئة نظام النسخ الاحتياطي مع التطبيق"""
    global backup_system
    backup_system = BackupSystem(app)
    return backup_system
