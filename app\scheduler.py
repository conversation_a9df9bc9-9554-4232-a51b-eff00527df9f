# -*- coding: utf-8 -*-
"""
نظام الجدولة للنسخ الاحتياطي التلقائي
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
from app.backup import backup_system

# إعداد التسجيل
logger = logging.getLogger('backup_scheduler')

class BackupScheduler:
    """نظام جدولة النسخ الاحتياطي"""
    
    def __init__(self, app=None):
        """تهيئة نظام الجدولة"""
        self.app = app
        self.scheduler = None
        self.job_id = 'auto_backup_job'
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة نظام الجدولة مع التطبيق"""
        self.app = app
        
        # إنشاء المجدول
        self.scheduler = BackgroundScheduler(
            timezone='UTC',
            job_defaults={
                'coalesce': True,
                'max_instances': 1,
                'misfire_grace_time': 300  # 5 minutes
            }
        )
        
        # إضافة مستمع للأحداث
        self.scheduler.add_listener(self._job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)
        
        # بدء المجدول
        try:
            self.scheduler.start()
            logger.info("تم بدء نظام جدولة النسخ الاحتياطي")
            
            # جدولة المهمة الأولى
            self.schedule_backup_job()
            
        except Exception as e:
            logger.error(f"خطأ في بدء نظام الجدولة: {str(e)}")
        
        # إيقاف المجدول عند إغلاق التطبيق
        import atexit
        atexit.register(self.shutdown)
    
    def _job_listener(self, event):
        """مستمع أحداث المهام"""
        if event.exception:
            logger.error(f"خطأ في تنفيذ مهمة النسخ الاحتياطي: {event.exception}")
        else:
            logger.info(f"تم تنفيذ مهمة النسخ الاحتياطي بنجاح في: {datetime.now()}")
    
    def schedule_backup_job(self):
        """جدولة مهمة النسخ الاحتياطي"""
        if not backup_system:
            logger.warning("نظام النسخ الاحتياطي غير متاح")
            return False
        
        config = backup_system.config
        
        # التحقق من تفعيل النسخ التلقائي
        if not config.get('auto_backup_enabled', True):
            logger.info("النسخ التلقائي معطل")
            self.remove_backup_job()
            return False
        
        # الحصول على فترة النسخ الاحتياطي
        backup_interval_hours = config.get('backup_interval_hours', 24)
        
        try:
            # إزالة المهمة الموجودة إذا كانت موجودة
            self.remove_backup_job()
            
            # إضافة مهمة جديدة
            self.scheduler.add_job(
                func=self._perform_backup,
                trigger=IntervalTrigger(hours=backup_interval_hours),
                id=self.job_id,
                name='Auto Backup Job',
                replace_existing=True
            )
            
            logger.info(f"تم جدولة النسخ الاحتياطي التلقائي كل {backup_interval_hours} ساعة")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في جدولة النسخ الاحتياطي: {str(e)}")
            return False
    
    def remove_backup_job(self):
        """إزالة مهمة النسخ الاحتياطي"""
        try:
            if self.scheduler and self.scheduler.get_job(self.job_id):
                self.scheduler.remove_job(self.job_id)
                logger.info("تم إزالة مهمة النسخ الاحتياطي التلقائي")
                return True
        except Exception as e:
            logger.error(f"خطأ في إزالة مهمة النسخ الاحتياطي: {str(e)}")
        
        return False
    
    def _perform_backup(self):
        """تنفيذ النسخ الاحتياطي"""
        try:
            logger.info("بدء النسخ الاحتياطي التلقائي...")
            
            if not backup_system:
                logger.error("نظام النسخ الاحتياطي غير متاح")
                return
            
            # التحقق من الحاجة للنسخ الاحتياطي
            if not backup_system.should_backup_now():
                logger.info("لا حاجة للنسخ الاحتياطي في الوقت الحالي")
                return
            
            # إنشاء اسم النسخة الاحتياطية
            timestamp = datetime.now()
            backup_name = f"auto_backup_{timestamp.strftime('%Y%m%d_%H%M%S')}"
            
            # إنشاء النسخة الاحتياطية
            result = backup_system.create_backup(backup_name)
            
            if result['success']:
                logger.info(f"تم إنشاء النسخة الاحتياطية التلقائية بنجاح: {backup_name}")
                
                # إرسال إشعار للمديرين (إذا كان نظام الإشعارات متاح)
                self._notify_backup_success(backup_name, result['backup_info'])
                
            else:
                error_msg = result.get('error', 'خطأ غير معروف')
                logger.error(f"فشل في إنشاء النسخة الاحتياطية التلقائية: {error_msg}")
                
                # إرسال إشعار بالفشل
                self._notify_backup_failure(error_msg)
        
        except Exception as e:
            logger.error(f"خطأ في تنفيذ النسخ الاحتياطي التلقائي: {str(e)}", exc_info=True)
            self._notify_backup_failure(str(e))
    
    def _notify_backup_success(self, backup_name, backup_info):
        """إرسال إشعار بنجاح النسخ الاحتياطي"""
        try:
            # محاولة إرسال إشعار للمديرين
            from app.models import User
            from app.notifications import notification_service
            
            if notification_service:
                # الحصول على المديرين النشطين
                admins = User.query.filter_by(is_admin=True, is_active=True).all()
                
                subject = "تم إنشاء نسخة احتياطية تلقائية"
                message = f"""
تم إنشاء نسخة احتياطية تلقائية بنجاح.

تفاصيل النسخة الاحتياطية:
- الاسم: {backup_name}
- التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- الحجم: {backup_info.get('size', 'غير محدد')}

نظام إدارة الصناديق البريدية
                """.strip()
                
                for admin in admins:
                    if admin.email_notifications and admin.email:
                        notification_service.send_email_notification(
                            admin.email, subject, message, None
                        )
        
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار نجاح النسخ الاحتياطي: {str(e)}")
    
    def _notify_backup_failure(self, error_msg):
        """إرسال إشعار بفشل النسخ الاحتياطي"""
        try:
            # محاولة إرسال إشعار للمديرين
            from app.models import User
            from app.notifications import notification_service
            
            if notification_service:
                # الحصول على المديرين النشطين
                admins = User.query.filter_by(is_admin=True, is_active=True).all()
                
                subject = "فشل في إنشاء النسخة الاحتياطية التلقائية"
                message = f"""
فشل في إنشاء النسخة الاحتياطية التلقائية.

تفاصيل الخطأ:
{error_msg}

التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

يرجى التحقق من النظام وإعدادات النسخ الاحتياطي.

نظام إدارة الصناديق البريدية
                """.strip()
                
                for admin in admins:
                    if admin.email_notifications and admin.email:
                        notification_service.send_email_notification(
                            admin.email, subject, message, None
                        )
        
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار فشل النسخ الاحتياطي: {str(e)}")
    
    def get_next_backup_time(self):
        """الحصول على موعد النسخة الاحتياطية التالية"""
        try:
            if self.scheduler and self.scheduler.get_job(self.job_id):
                job = self.scheduler.get_job(self.job_id)
                return job.next_run_time
        except Exception as e:
            logger.error(f"خطأ في الحصول على موعد النسخة الاحتياطية التالية: {str(e)}")
        
        return None
    
    def is_backup_job_scheduled(self):
        """التحقق من جدولة مهمة النسخ الاحتياطي"""
        try:
            if self.scheduler:
                job = self.scheduler.get_job(self.job_id)
                return job is not None
        except Exception as e:
            logger.error(f"خطأ في التحقق من جدولة النسخ الاحتياطي: {str(e)}")
        
        return False
    
    def trigger_backup_now(self):
        """تشغيل النسخ الاحتياطي فوراً"""
        try:
            if self.scheduler:
                # إضافة مهمة فورية
                self.scheduler.add_job(
                    func=self._perform_backup,
                    trigger='date',
                    run_date=datetime.now(),
                    id='manual_backup_job',
                    name='Manual Backup Job',
                    replace_existing=True
                )
                logger.info("تم تشغيل النسخ الاحتياطي فوراً")
                return True
        except Exception as e:
            logger.error(f"خطأ في تشغيل النسخ الاحتياطي فوراً: {str(e)}")
        
        return False
    
    def update_schedule(self):
        """تحديث جدولة النسخ الاحتياطي"""
        return self.schedule_backup_job()
    
    def get_scheduler_status(self):
        """الحصول على حالة المجدول"""
        try:
            if not self.scheduler:
                return {
                    'running': False,
                    'job_scheduled': False,
                    'next_run_time': None,
                    'error': 'المجدول غير متاح'
                }
            
            return {
                'running': self.scheduler.running,
                'job_scheduled': self.is_backup_job_scheduled(),
                'next_run_time': self.get_next_backup_time(),
                'jobs_count': len(self.scheduler.get_jobs())
            }
        
        except Exception as e:
            logger.error(f"خطأ في الحصول على حالة المجدول: {str(e)}")
            return {
                'running': False,
                'job_scheduled': False,
                'next_run_time': None,
                'error': str(e)
            }
    
    def shutdown(self):
        """إيقاف المجدول"""
        try:
            if self.scheduler and self.scheduler.running:
                self.scheduler.shutdown(wait=False)
                logger.info("تم إيقاف نظام جدولة النسخ الاحتياطي")
        except Exception as e:
            logger.error(f"خطأ في إيقاف المجدول: {str(e)}")


# تهيئة نظام الجدولة
backup_scheduler = None

def init_scheduler(app):
    """تهيئة نظام الجدولة مع التطبيق"""
    global backup_scheduler
    backup_scheduler = BackupScheduler(app)
    return backup_scheduler
