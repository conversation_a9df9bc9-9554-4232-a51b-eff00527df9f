{% extends "base.html" %}

{% block title %}تغيير كلمة المرور - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-key me-2"></i>تغيير كلمة المرور</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">تغيير كلمة المرور</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>تحديث كلمة المرور
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        لأمان حسابك، يرجى إدخال كلمة المرور الحالية أولاً
                    </div>

                    <form id="changePasswordForm" method="POST">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">
                                <i class="fas fa-lock me-2"></i>كلمة المرور الحالية <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                    <i class="fas fa-eye" id="current_password_icon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">
                                <i class="fas fa-key me-2"></i>كلمة المرور الجديدة <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                    <i class="fas fa-eye" id="new_password_icon"></i>
                                </button>
                            </div>
                            <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-check-double me-2"></i>تأكيد كلمة المرور الجديدة <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="6">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                    <i class="fas fa-eye" id="confirm_password_icon"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Password Strength Indicator -->
                        <div class="mb-3">
                            <label class="form-label">قوة كلمة المرور:</label>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar" id="passwordStrength" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small id="passwordStrengthText" class="form-text"></small>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <span class="normal-text">
                                    <i class="fas fa-save me-2"></i>تحديث كلمة المرور
                                </span>
                                <span class="loading d-none">
                                    <i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('changePasswordForm');
    const newPasswordInput = document.getElementById('new_password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const strengthBar = document.getElementById('passwordStrength');
    const strengthText = document.getElementById('passwordStrengthText');

    // Password strength checker
    newPasswordInput.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        updatePasswordStrength(strength);
    });

    // Password match checker
    confirmPasswordInput.addEventListener('input', checkPasswordMatch);

    function calculatePasswordStrength(password) {
        let score = 0;
        let feedback = [];

        if (password.length >= 6) score += 20;
        else feedback.push('6 أحرف على الأقل');

        if (password.length >= 8) score += 10;
        if (/[a-z]/.test(password)) score += 15;
        if (/[A-Z]/.test(password)) score += 15;
        if (/[0-9]/.test(password)) score += 15;
        if (/[^A-Za-z0-9]/.test(password)) score += 25;

        return { score, feedback };
    }

    function updatePasswordStrength(strength) {
        const { score, feedback } = strength;
        
        strengthBar.style.width = score + '%';
        
        if (score < 30) {
            strengthBar.className = 'progress-bar bg-danger';
            strengthText.textContent = 'ضعيفة';
            strengthText.className = 'form-text text-danger';
        } else if (score < 60) {
            strengthBar.className = 'progress-bar bg-warning';
            strengthText.textContent = 'متوسطة';
            strengthText.className = 'form-text text-warning';
        } else if (score < 80) {
            strengthBar.className = 'progress-bar bg-info';
            strengthText.textContent = 'جيدة';
            strengthText.className = 'form-text text-info';
        } else {
            strengthBar.className = 'progress-bar bg-success';
            strengthText.textContent = 'قوية';
            strengthText.className = 'form-text text-success';
        }
    }

    function checkPasswordMatch() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        if (confirmPassword && newPassword !== confirmPassword) {
            setFieldError(confirmPasswordInput, 'كلمة المرور وتأكيدها غير متطابقتين');
        } else if (confirmPassword) {
            setFieldValid(confirmPasswordInput);
        }
    }

    function setFieldError(field, message) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        const feedback = field.parentElement.nextElementSibling;
        if (feedback && feedback.classList.contains('invalid-feedback')) {
            feedback.textContent = message;
        }
    }

    function setFieldValid(field) {
        field.classList.add('is-valid');
        field.classList.remove('is-invalid');
    }

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = form.querySelector('button[type="submit"]');
        const normalText = submitBtn.querySelector('.normal-text');
        const loadingText = submitBtn.querySelector('.loading');

        // Show loading state
        normalText.classList.add('d-none');
        loadingText.classList.remove('d-none');
        submitBtn.disabled = true;

        // Prepare form data
        const formData = new FormData(form);
        const data = {
            current_password: formData.get('current_password'),
            new_password: formData.get('new_password'),
            confirm_password: formData.get('confirm_password')
        };

        // Validate password match
        if (data.new_password !== data.confirm_password) {
            showAlert('danger', 'كلمة المرور الجديدة وتأكيدها غير متطابقتين');
            resetButton();
            return;
        }

        // Send AJAX request
        fetch(form.action || window.location.pathname, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message || 'تم تغيير كلمة المرور بنجاح');
                form.reset();
                // Reset password strength indicator
                strengthBar.style.width = '0%';
                strengthText.textContent = '';
                // Remove validation classes
                form.querySelectorAll('.is-valid, .is-invalid').forEach(el => {
                    el.classList.remove('is-valid', 'is-invalid');
                });
                
                // Redirect after success
                setTimeout(() => {
                    window.location.href = "{{ url_for('main.index') }}";
                }, 2000);
            } else {
                showAlert('danger', data.error || 'حدث خطأ غير متوقع');
            }
            resetButton();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'حدث خطأ في الاتصال');
            resetButton();
        });

        function resetButton() {
            normalText.classList.remove('d-none');
            loadingText.classList.add('d-none');
            submitBtn.disabled = false;
        }
    });

    function showAlert(type, message) {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.alert:not(.alert-info)');
        existingAlerts.forEach(alert => alert.remove());

        // Create new alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert alert at the top of the card body
        const cardBody = document.querySelector('.card-body');
        const infoAlert = cardBody.querySelector('.alert-info');
        cardBody.insertBefore(alertDiv, infoAlert.nextSibling);
    }
});
</script>
{% endblock %}
