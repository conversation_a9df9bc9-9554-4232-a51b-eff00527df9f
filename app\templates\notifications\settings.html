{% extends "base.html" %}

{% block title %}إعدادات الإشعارات - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cog me-2"></i>إعدادات الإشعارات</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('notifications.index') }}">الإشعارات</a></li>
                        <li class="breadcrumb-item active">الإعدادات</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- إعدادات البريد الإلكتروني -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-envelope me-2"></i>إعدادات البريد الإلكتروني
                    </h5>
                </div>
                <div class="card-body">
                    <form id="emailSettingsForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="mailServer" class="form-label">خادم البريد الإلكتروني</label>
                                <input type="text" class="form-control" id="mailServer" 
                                       value="smtp.gmail.com" placeholder="smtp.gmail.com">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="mailPort" class="form-label">منفذ الخادم</label>
                                <input type="number" class="form-control" id="mailPort" 
                                       value="587" placeholder="587">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="mailUsername" class="form-label">اسم المستخدم</label>
                                <input type="email" class="form-control" id="mailUsername" 
                                       placeholder="<EMAIL>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="mailPassword" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="mailPassword" 
                                       placeholder="كلمة مرور التطبيق">
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="mailUseTLS" checked>
                                <label class="form-check-label" for="mailUseTLS">
                                    استخدام TLS للأمان
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <button type="button" class="btn btn-outline-primary" onclick="testEmailConnection()">
                                <i class="fas fa-vial me-2"></i>اختبار الاتصال
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إعدادات الرسائل النصية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sms me-2"></i>إعدادات الرسائل النصية (Twilio)
                    </h5>
                </div>
                <div class="card-body">
                    <form id="smsSettingsForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="twilioAccountSid" class="form-label">Account SID</label>
                                <input type="text" class="form-control" id="twilioAccountSid" 
                                       placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="twilioAuthToken" class="form-label">Auth Token</label>
                                <input type="password" class="form-control" id="twilioAuthToken" 
                                       placeholder="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="twilioPhoneNumber" class="form-label">رقم الهاتف المرسل</label>
                            <input type="tel" class="form-control" id="twilioPhoneNumber" 
                                   placeholder="+**********">
                        </div>
                        <div class="mb-3">
                            <button type="button" class="btn btn-outline-primary" onclick="testSMSConnection()">
                                <i class="fas fa-vial me-2"></i>اختبار الاتصال
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إعدادات الإشعارات التلقائية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>إعدادات الإشعارات التلقائية
                    </h5>
                </div>
                <div class="card-body">
                    <form id="autoNotificationForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="notificationDays" class="form-label">أيام الإشعار قبل انتهاء الاشتراك</label>
                                <select class="form-select" id="notificationDays" multiple>
                                    <option value="30" selected>30 يوم</option>
                                    <option value="15" selected>15 يوم</option>
                                    <option value="7" selected>7 أيام</option>
                                    <option value="3" selected>3 أيام</option>
                                    <option value="1" selected>يوم واحد</option>
                                </select>
                                <div class="form-text">اختر الأيام التي تريد إرسال إشعارات فيها</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="notificationTime" class="form-label">وقت الإرسال اليومي</label>
                                <input type="time" class="form-control" id="notificationTime" value="09:00">
                                <div class="form-text">الوقت المفضل لإرسال الإشعارات يومياً</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableAutoNotifications" checked>
                                <label class="form-check-label" for="enableAutoNotifications">
                                    تفعيل الإشعارات التلقائية
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary" onclick="saveAllSettings()">
                            <i class="fas fa-save me-2"></i>حفظ جميع الإعدادات
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetSettings()">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                        <a href="{{ url_for('notifications.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- معلومات مساعدة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات مساعدة
                    </h5>
                </div>
                <div class="card-body">
                    <h6>إعداد Gmail:</h6>
                    <ul class="small">
                        <li>استخدم كلمة مرور التطبيق وليس كلمة مرور حسابك</li>
                        <li>فعّل التحقق بخطوتين أولاً</li>
                        <li>أنشئ كلمة مرور تطبيق من إعدادات الأمان</li>
                    </ul>
                    
                    <h6 class="mt-3">إعداد Twilio:</h6>
                    <ul class="small">
                        <li>سجل حساب في Twilio.com</li>
                        <li>احصل على Account SID و Auth Token</li>
                        <li>اشتري رقم هاتف للإرسال</li>
                    </ul>
                </div>
            </div>

            <!-- حالة الخدمات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-heartbeat me-2"></i>حالة الخدمات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>البريد الإلكتروني:</span>
                        <span class="badge bg-secondary" id="emailStatus">غير محدد</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>الرسائل النصية:</span>
                        <span class="badge bg-secondary" id="smsStatus">غير محدد</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>الإشعارات التلقائية:</span>
                        <span class="badge bg-success" id="autoNotificationStatus">مفعلة</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function testEmailConnection() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';
    btn.disabled = true;

    // Simulate email test
    setTimeout(() => {
        showAlert('info', 'اختبار البريد الإلكتروني - هذه ميزة تجريبية');
        document.getElementById('emailStatus').textContent = 'متصل';
        document.getElementById('emailStatus').className = 'badge bg-success';
        
        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 2000);
}

function testSMSConnection() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';
    btn.disabled = true;

    // Simulate SMS test
    setTimeout(() => {
        showAlert('info', 'اختبار الرسائل النصية - هذه ميزة تجريبية');
        document.getElementById('smsStatus').textContent = 'متصل';
        document.getElementById('smsStatus').className = 'badge bg-success';
        
        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 2000);
}

function saveAllSettings() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    btn.disabled = true;

    // Simulate save
    setTimeout(() => {
        showAlert('success', 'تم حفظ الإعدادات بنجاح');
        
        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 1500);
}

function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        location.reload();
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Load current settings on page load
document.addEventListener('DOMContentLoaded', function() {
    // This would load actual settings from the server
    console.log('Loading notification settings...');
});
</script>
{% endblock %}
