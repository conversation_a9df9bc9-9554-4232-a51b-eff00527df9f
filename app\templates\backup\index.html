{% extends "base.html" %}

{% block title %}إدارة النسخ الاحتياطي - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-database me-2"></i>إدارة النسخ الاحتياطي</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">النسخ الاحتياطي</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- إحصائيات النسخ الاحتياطي -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                عدد النسخ الاحتياطية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_backups }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-copy fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الحجم الإجمالي
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_size_str }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hdd fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                آخر نسخة احتياطية
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">
                                {% if last_backup %}
                                    {{ last_backup.timestamp[:10] }}
                                {% else %}
                                    لا توجد نسخ
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                النسخ التلقائي
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">
                                {% if config.get('auto_backup_enabled', True) %}
                                    <span class="text-success">مفعل</span>
                                {% else %}
                                    <span class="text-danger">معطل</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-robot fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>إجراءات النسخ الاحتياطي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <button type="button" class="btn btn-success btn-lg w-100" onclick="createBackup()">
                                <i class="fas fa-shield-alt me-2"></i>إنشاء نسخة احتياطية جديدة
                            </button>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button type="button" class="btn btn-primary btn-lg w-100" onclick="triggerAutoBackup()">
                                <i class="fas fa-sync-alt me-2"></i>تشغيل النسخ التلقائي
                            </button>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="{{ url_for('backup.settings') }}" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-cogs me-2"></i>إعدادات النسخ الاحتياطي
                            </a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <a href="{{ url_for('backup.monitor') }}" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-chart-line me-2"></i>مراقبة النظام
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button type="button" class="btn btn-outline-secondary btn-lg w-100" onclick="showBackupGuide()">
                                <i class="fas fa-question-circle me-2"></i>دليل الاستخدام
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة النسخ الاحتياطية -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>النسخ الاحتياطية المتاحة
                    </h5>
                </div>
                <div class="card-body">
                    {% if backups %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>اسم النسخة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الحجم</th>
                                        <th>معلومات إضافية</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for backup in backups %}
                                        <tr>
                                            <td>
                                                <strong>{{ backup.name }}</strong>
                                            </td>
                                            <td>
                                                {{ backup.timestamp[:19].replace('T', ' ') }}
                                            </td>
                                            <td>
                                                {% set size = backup.get('size', 0) %}
                                                {% if size > 1024 * 1024 * 1024 %}
                                                    {{ "%.2f"|format(size / (1024 * 1024 * 1024)) }} GB
                                                {% elif size > 1024 * 1024 %}
                                                    {{ "%.2f"|format(size / (1024 * 1024)) }} MB
                                                {% else %}
                                                    {{ "%.2f"|format(size / 1024) }} KB
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if backup.get('includes_uploads') %}
                                                    <span class="badge bg-info">يتضمن الملفات</span>
                                                {% endif %}
                                                {% if backup.get('db_file') %}
                                                    <span class="badge bg-success">قاعدة البيانات</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ url_for('backup.download_backup', backup_name=backup.name + ('.zip' if backup.path.endswith('.zip') else '')) }}"
                                                       class="btn btn-sm btn-outline-primary" title="تحميل النسخة الاحتياطية">
                                                        <i class="fas fa-cloud-download-alt"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-warning"
                                                            onclick="restoreBackup('{{ backup.path }}')" title="استعادة النسخة الاحتياطية">
                                                        <i class="fas fa-history"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"
                                                            onclick="viewBackupDetails('{{ backup.path }}')" title="عرض التفاصيل">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                            onclick="deleteBackup('{{ backup.path }}', '{{ backup.name }}')" title="حذف النسخة الاحتياطية">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-database fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد نسخ احتياطية</h5>
                            <p class="text-muted">قم بإنشاء أول نسخة احتياطية للنظام</p>
                            <button type="button" class="btn btn-primary" onclick="createBackup()">
                                <i class="fas fa-plus me-2"></i>إنشاء نسخة احتياطية
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإنشاء نسخة احتياطية -->
<div class="modal fade" id="createBackupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء نسخة احتياطية جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createBackupForm">
                    <div class="mb-3">
                        <label for="backupName" class="form-label">اسم النسخة الاحتياطية (اختياري)</label>
                        <input type="text" class="form-control" id="backupName" name="backup_name" 
                               placeholder="سيتم إنشاء اسم تلقائي إذا ترك فارغاً">
                        <div class="form-text">إذا ترك فارغاً، سيتم استخدام التاريخ والوقت الحالي</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="submitCreateBackup()">
                    <span class="normal-text">
                        <i class="fas fa-plus me-2"></i>إنشاء النسخة
                    </span>
                    <span class="loading d-none">
                        <i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...
                    </span>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function createBackup() {
    const modal = new bootstrap.Modal(document.getElementById('createBackupModal'));
    modal.show();
}

function submitCreateBackup() {
    const form = document.getElementById('createBackupForm');
    const submitBtn = document.querySelector('#createBackupModal .btn-success');
    const normalText = submitBtn.querySelector('.normal-text');
    const loadingText = submitBtn.querySelector('.loading');
    
    // Show loading state
    normalText.classList.add('d-none');
    loadingText.classList.remove('d-none');
    submitBtn.disabled = true;
    
    const formData = new FormData(form);
    const data = {
        backup_name: formData.get('backup_name')
    };
    
    fetch('{{ url_for("backup.create_backup") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || 'تم إنشاء النسخة الاحتياطية بنجاح');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.error || 'حدث خطأ غير متوقع');
        }
        resetButton();
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في الاتصال');
        resetButton();
    });
    
    function resetButton() {
        normalText.classList.remove('d-none');
        loadingText.classList.add('d-none');
        submitBtn.disabled = false;
    }
}

function restoreBackup(backupPath) {
    if (!confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
        return;
    }
    
    fetch('{{ url_for("backup.restore_backup") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({backup_path: backupPath})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || 'تم استعادة النسخة الاحتياطية بنجاح');
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showAlert('danger', data.error || 'حدث خطأ في استعادة النسخة الاحتياطية');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في الاتصال');
    });
}

function deleteBackup(backupPath, backupName) {
    if (!confirm(`هل أنت متأكد من حذف النسخة الاحتياطية "${backupName}"؟ لا يمكن التراجع عن هذا الإجراء.`)) {
        return;
    }
    
    fetch('{{ url_for("backup.delete_backup") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({backup_path: backupPath})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || 'تم حذف النسخة الاحتياطية بنجاح');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.error || 'حدث خطأ في حذف النسخة الاحتياطية');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في الاتصال');
    });
}

function triggerAutoBackup() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التشغيل...';
    btn.disabled = true;

    fetch('{{ url_for("backup.api_trigger_backup") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || 'تم تشغيل النسخ الاحتياطي بنجاح');
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('danger', data.error || 'حدث خطأ في تشغيل النسخ الاحتياطي');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في الاتصال');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function viewBackupDetails(backupPath) {
    // This would show a modal with backup details
    showAlert('info', 'عرض تفاصيل النسخة الاحتياطية - قيد التطوير');
}

function showBackupGuide() {
    const guideContent = `
        <div class="text-start">
            <h5>دليل استخدام نظام النسخ الاحتياطي:</h5>
            <ul>
                <li><strong>إنشاء نسخة احتياطية:</strong> انقر على "إنشاء نسخة احتياطية جديدة" لحفظ البيانات الحالية</li>
                <li><strong>النسخ التلقائي:</strong> يتم تشغيله تلقائياً كل 24 ساعة</li>
                <li><strong>الاستعادة:</strong> استخدم زر "استعادة" لإرجاع البيانات من نسخة سابقة</li>
                <li><strong>التحميل:</strong> حمّل النسخ الاحتياطية لحفظها خارجياً</li>
                <li><strong>المراقبة:</strong> تابع حالة النظام والتنبيهات</li>
            </ul>
            <div class="alert alert-warning mt-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> تأكد من اختبار النسخ الاحتياطية بانتظام للتأكد من سلامتها
            </div>
        </div>
    `;

    // Create and show modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-book me-2"></i>دليل النسخ الاحتياطي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${guideContent}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
}

function showAlert(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert alert at the top of the container
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.children[1]);
}
</script>
{% endblock %}
