# -*- coding: utf-8 -*-
"""
Notification system for postal box management
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from datetime import date, timedelta
from flask import current_app
from app.models import Subscriber, Subscription, Notification, User
from app.extensions import db
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NotificationService:
    """Service class for handling notifications"""
    
    def __init__(self):
        self.smtp_server = None
        self.smtp_port = None
        self.smtp_username = None
        self.smtp_password = None
        self.from_email = None
    
    def _setup_email_config(self):
        """Setup email configuration from app config"""
        if current_app:
            self.smtp_server = current_app.config.get('MAIL_SERVER')
            self.smtp_port = current_app.config.get('MAIL_PORT', 587)
            self.smtp_username = current_app.config.get('MAIL_USERNAME')
            self.smtp_password = current_app.config.get('MAIL_PASSWORD')
            self.from_email = self.smtp_username
    
    def send_email_notification(self, to_email, subject, message, subscription_id):
        """Send email notification"""
        try:
            self._setup_email_config()
            
            if not all([self.smtp_server, self.smtp_username, self.smtp_password]):
                logger.warning("Email configuration not complete")
                return False
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # Add body to email
            msg.attach(MIMEText(message, 'plain', 'utf-8'))
            
            # Create SMTP session
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()  # Enable security
            server.login(self.smtp_username, self.smtp_password)
            
            # Send email
            text = msg.as_string()
            server.sendmail(self.from_email, to_email, text)
            server.quit()
            
            # Log successful notification
            self._log_notification(subscription_id, 'email', message, True)
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            # Log failed notification
            self._log_notification(subscription_id, 'email', message, False, str(e))
            logger.error(f"Failed to send email to {to_email}: {str(e)}")
            return False
    
    def send_sms_notification(self, phone_number, message, subscription_id):
        """Send SMS notification using Twilio"""
        try:
            # This would require Twilio integration
            # For now, we'll just log it
            logger.info(f"SMS would be sent to {phone_number}: {message}")
            
            # Log notification (simulated as successful for demo)
            self._log_notification(subscription_id, 'sms', message, True)
            return True
            
        except Exception as e:
            self._log_notification(subscription_id, 'sms', message, False, str(e))
            logger.error(f"Failed to send SMS to {phone_number}: {str(e)}")
            return False
    
    def _log_notification(self, subscription_id, notification_type, message, is_successful, error_message=None):
        """Log notification attempt to database"""
        try:
            subscription = Subscription.query.get(subscription_id)
            if subscription:
                notification = Notification(
                    subscription_id=subscription_id,
                    notification_type=notification_type,
                    message=message,
                    is_successful=is_successful,
                    error_message=error_message,
                    days_before_expiry=subscription.days_remaining
                )
                db.session.add(notification)
                db.session.commit()
        except Exception as e:
            logger.error(f"Failed to log notification: {str(e)}")
            db.session.rollback()
    
    def create_expiry_message(self, subscriber, subscription, days_remaining):
        """Create expiry notification message"""
        if days_remaining <= 0:
            status = "انتهت صلاحية"
        elif days_remaining <= 7:
            status = f"ستنتهي خلال {days_remaining} أيام"
        else:
            status = f"ستنتهي خلال {days_remaining} يوم"
        
        message = f"""
عزيزي/عزيزتي {subscriber.name}،

نود إعلامكم أن اشتراككم في الصندوق البريدي رقم {subscriber.box_number} {status}.

تفاصيل الاشتراك:
- رقم الصندوق: {subscriber.box_number}
- تاريخ انتهاء الاشتراك: {subscription.end_date.strftime('%Y-%m-%d')}
- حجم الصندوق: {subscriber.box_size}

للتجديد أو الاستفسار، يرجى التواصل معنا على:
- الهاتف: [رقم الهاتف]
- البريد الإلكتروني: [البريد الإلكتروني]

شكراً لثقتكم بخدماتنا.

إدارة الصناديق البريدية
        """.strip()
        
        return message
    
    def check_and_send_expiry_notifications(self, days_before=[15, 7, 3, 1]):
        """Check for expiring subscriptions and send notifications"""
        logger.info("Starting expiry notification check...")

        notifications_sent = 0

        # Get all active users with their notification preferences
        active_users = User.query.filter_by(is_active=True).all()

        for days in days_before:
            target_date = date.today() + timedelta(days=days)

            # Find subscriptions expiring on target date
            expiring_subscriptions = Subscription.query.join(Subscriber).filter(
                Subscription.end_date == target_date,
                Subscription.is_active == True,
                Subscriber.is_active == True
            ).all()

            for subscription in expiring_subscriptions:
                subscriber = subscription.subscriber

                # Check if notification was already sent for this period
                existing_notification = Notification.query.filter_by(
                    subscription_id=subscription.id,
                    days_before_expiry=days
                ).filter(
                    Notification.sent_at >= date.today()
                ).first()

                if existing_notification:
                    continue  # Skip if already notified today

                # Create message
                message = self.create_expiry_message(subscriber, subscription, days)
                subject = f"تنبيه: اشتراك الصندوق البريدي رقم {subscriber.box_number}"

                # Send notifications to all active users based on their preferences
                for user in active_users:
                    # Check if this user wants notifications for this timing
                    if days <= user.notification_days_before:
                        # Send email notification if user has email notifications enabled
                        if user.email_notifications and user.email:
                            if self.send_email_notification(user.email, subject, message, subscription.id):
                                notifications_sent += 1

                        # Send SMS notification if user has SMS notifications enabled
                        if user.sms_notifications and subscriber.phone:
                            if self.send_sms_notification(subscriber.phone, message, subscription.id):
                                notifications_sent += 1
                
                # Send SMS notification
                if subscriber.phone:
                    sms_message = f"تنبيه: اشتراك الصندوق {subscriber.box_number} ينتهي في {days} أيام. تاريخ الانتهاء: {subscription.end_date.strftime('%Y-%m-%d')}"
                    if self.send_sms_notification(subscriber.phone, sms_message, subscription.id):
                        notifications_sent += 1
        
        logger.info(f"Expiry notification check completed. Sent {notifications_sent} notifications.")
        return notifications_sent

    def send_notification_to_users(self, message, subject, subscription_id, notification_type='all'):
        """Send notification to all active users based on their preferences"""
        active_users = User.query.filter_by(is_active=True).all()
        notifications_sent = 0

        for user in active_users:
            if notification_type in ['all', 'email'] and user.email_notifications and user.email:
                if self.send_email_notification(user.email, subject, message, subscription_id):
                    notifications_sent += 1

            if notification_type in ['all', 'sms'] and user.sms_notifications:
                # For SMS, we would need the subscriber's phone number
                # This is a placeholder for SMS functionality
                logger.info(f"SMS notification would be sent to user {user.username}")

        return notifications_sent
    
    def send_manual_notification(self, subscriber_id, message, notification_type='email'):
        """Send manual notification to a specific subscriber"""
        try:
            subscriber = Subscriber.query.get(subscriber_id)
            if not subscriber:
                return False, "المشترك غير موجود"
            
            current_subscription = subscriber.current_subscription
            if not current_subscription:
                return False, "لا يوجد اشتراك نشط للمشترك"
            
            success = False
            
            if notification_type == 'email' and subscriber.email:
                subject = f"إشعار من إدارة الصناديق البريدية - صندوق رقم {subscriber.box_number}"
                success = self.send_email_notification(subscriber.email, subject, message, current_subscription.id)
            elif notification_type == 'sms' and subscriber.phone:
                success = self.send_sms_notification(subscriber.phone, message, current_subscription.id)
            else:
                return False, "نوع الإشعار غير صحيح أو بيانات الاتصال غير متوفرة"
            
            if success:
                return True, "تم إرسال الإشعار بنجاح"
            else:
                return False, "فشل في إرسال الإشعار"
                
        except Exception as e:
            logger.error(f"Failed to send manual notification: {str(e)}")
            return False, f"حدث خطأ: {str(e)}"

# Global notification service instance
notification_service = NotificationService()

def run_daily_notification_check():
    """Function to be called by scheduler for daily notification check"""
    try:
        with current_app.app_context():
            return notification_service.check_and_send_expiry_notifications()
    except Exception as e:
        logger.error(f"Daily notification check failed: {str(e)}")
        return 0
