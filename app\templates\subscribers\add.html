{% extends "base.html" %}

{% block title %}إضافة مشترك جديد - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إضافة مشترك جديد</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('subscribers.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i> العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">بيانات المشترك</h5>
            </div>
            <div class="card-body">
                <form id="subscriberForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="box_number" class="form-label">رقم الصندوق <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="box_number" name="box_number" required>
                            <div class="invalid-feedback" id="box_number_error"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم المشترك <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback" id="name_error"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="phone" name="phone" required>
                            <div class="invalid-feedback" id="phone_error"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="passport_number" class="form-label">رقم الهوية/الجواز <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="passport_number" name="passport_number" required>
                            <div class="invalid-feedback" id="passport_number_error"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="box_size" class="form-label">حجم الصندوق <span class="text-danger">*</span></label>
                            <select class="form-select" id="box_size" name="box_size" required>
                                <option value="">اختر حجم الصندوق</option>
                                <option value="Small">صغير</option>
                                <option value="Medium">متوسط</option>
                                <option value="Large">كبير</option>
                            </select>
                            <div class="invalid-feedback" id="box_size_error"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email">
                            <div class="invalid-feedback" id="email_error"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                        <div class="invalid-feedback" id="address_error"></div>
                    </div>

                    <hr>

                    <h6 class="mb-3">بيانات الاشتراك (اختياري)</h6>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="add_subscription">
                        <label class="form-check-label" for="add_subscription">
                            إضافة اشتراك مع المشترك
                        </label>
                    </div>

                    <div id="subscription_fields" class="d-none">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="start_date" class="form-label">تاريخ البدء</label>
                                <input type="date" class="form-control" id="start_date" name="start_date">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="end_date" class="form-label">تاريخ الانتهاء</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="amount_paid" class="form-label">المبلغ المدفوع</label>
                                <input type="number" class="form-control" id="amount_paid" name="amount_paid" step="0.001" min="0">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="payment_date" class="form-label">تاريخ الدفع</label>
                                <input type="date" class="form-control" id="payment_date" name="payment_date">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ المشترك
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">إرشادات</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        سيتم التحقق من تفرد رقم الصندوق ورقم الهوية
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-calendar text-warning me-2"></i>
                        يمكن إضافة الاشتراك لاحقاً من صفحة تفاصيل المشترك
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-envelope text-primary me-2"></i>
                        البريد الإلكتروني سيُستخدم للإشعارات
                    </li>
                </ul>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">أحجام الصناديق</h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>Small:</strong> للرسائل والوثائق الصغيرة
                </div>
                <div class="mb-2">
                    <strong>Medium:</strong> للطرود الصغيرة والمتوسطة
                </div>
                <div class="mb-2">
                    <strong>Large:</strong> للطرود االكبيرة والمواد الضخمة
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Toggle subscription fields
document.getElementById('add_subscription').addEventListener('change', function() {
    const subscriptionFields = document.getElementById('subscription_fields');
    if (this.checked) {
        subscriptionFields.classList.remove('d-none');
        // Set default dates
        const today = new Date().toISOString().split('T')[0];
        const nextYear = new Date();
        nextYear.setFullYear(nextYear.getFullYear() + 1);
        
        document.getElementById('start_date').value = today;
        document.getElementById('end_date').value = nextYear.toISOString().split('T')[0];
        document.getElementById('payment_date').value = today;
    } else {
        subscriptionFields.classList.add('d-none');
    }
});

// Real-time validation
let validationTimeout;

function validateField(field, value, excludeId = null) {
    clearTimeout(validationTimeout);
    validationTimeout = setTimeout(() => {
        fetch(`/subscribers/api/check-unique?field=${field}&value=${encodeURIComponent(value)}${excludeId ? `&exclude_id=${excludeId}` : ''}`)
            .then(response => response.json())
            .then(data => {
                const input = document.getElementById(field);
                const errorDiv = document.getElementById(field + '_error');
                
                if (data.exists) {
                    input.classList.add('is-invalid');
                    errorDiv.textContent = field === 'box_number' ? 'رقم الصندوق موجود مسبقاً' : 'رقم الهوية/الجواز موجود مسبقاً';
                } else {
                    input.classList.remove('is-invalid');
                    input.classList.add('is-valid');
                    errorDiv.textContent = '';
                }
            })
            .catch(error => console.error('Validation error:', error));
    }, 500);
}

// Add validation listeners
document.getElementById('box_number').addEventListener('input', function() {
    if (this.value.trim()) {
        validateField('box_number', this.value.trim());
    }
});

document.getElementById('passport_number').addEventListener('input', function() {
    if (this.value.trim()) {
        validateField('passport_number', this.value.trim());
    }
});

// Form submission
document.getElementById('subscriberForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    // Add subscription data if checkbox is checked
    if (document.getElementById('add_subscription').checked) {
        data.add_subscription = true;
    }
    
    fetch('/subscribers/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = `/subscribers/${data.id}`;
        } else {
            // Show validation errors
            Object.keys(data.errors || {}).forEach(field => {
                const input = document.getElementById(field);
                const errorDiv = document.getElementById(field + '_error');
                if (input && errorDiv) {
                    input.classList.add('is-invalid');
                    errorDiv.textContent = data.errors[field];
                }
            });
            
            if (data.error) {
                alert('حدث خطأ: ' + data.error);
            }
        }
    })
    .catch(error => {
        console.error('Submit error:', error);
        alert('حدث خطأ أثناء الحفظ');
    });
});

// Clear validation on input
document.querySelectorAll('.form-control, .form-select').forEach(input => {
    input.addEventListener('input', function() {
        this.classList.remove('is-invalid', 'is-valid');
        const errorDiv = document.getElementById(this.id + '_error');
        if (errorDiv) {
            errorDiv.textContent = '';
        }
    });
});
</script>
{% endblock %}
