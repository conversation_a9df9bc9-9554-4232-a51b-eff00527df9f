<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الصناديق البريدية{% endblock %}</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #343a40;
        }
        
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        
        .sidebar .nav-link i {
            margin-left: 0.5rem;
            width: 1.25rem;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }
        
        .btn {
            border-radius: 0.5rem;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            background-color: #f8f9fa;
        }
        
        .badge {
            font-size: 0.75rem;
        }
        
        .alert {
            border: none;
            border-radius: 0.75rem;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .search-result-item {
            padding: 0.75rem;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
        }
        
        .search-result-item:hover {
            background-color: #f8f9fa;
        }
        
        .status-active { color: #198754; }
        .status-expired { color: #dc3545; }
        .status-expiring { color: #fd7e14; }
        
        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
            }
            
            .main-content {
                padding: 1rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-mailbox"></i>
                نظام إدارة الصناديق البريدية
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav me-auto">
                    <!-- Search Box -->
                    <div class="search-box me-3">
                        <input type="text" class="form-control" id="globalSearch" placeholder="البحث..." style="width: 300px;">
                        <div id="searchResults" class="search-results d-none"></div>
                    </div>
                </div>
                
                <!-- قائمة المستخدم -->
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-2"></i>
                            <span>{{ current_user.full_name if current_user.is_authenticated else 'مستخدم' }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            {% if current_user.is_authenticated %}
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                        <i class="fas fa-user me-2"></i>الملف الشخصي
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                        <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                                    </a>
                                </li>
                                {% if current_user.is_admin %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('auth.users') }}">
                                            <i class="fas fa-users-cog me-2"></i>إدارة المستخدمين
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('auth.register') }}">
                                            <i class="fas fa-user-plus me-2"></i>إضافة مستخدم
                                        </a>
                                    </li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="{{ url_for('auth.logout') }}">
                                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                    </a>
                                </li>
                            {% else %}
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('auth.login') }}">
                                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>

                <!-- تم إخفاء أيقونة الجرس حسب الطلب -->
                <div class="navbar-nav d-none">
                    <a class="nav-link" href="#" onclick="showNotifications()">
                        <i class="fas fa-bell"></i>
                        <span class="badge bg-danger" id="notificationCount">0</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'main.index' }}" href="{{ url_for('main.index') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint and 'subscribers' in request.endpoint }}" href="{{ url_for('subscribers.index') }}">
                                <i class="fas fa-users"></i>
                                المشتركون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint and 'reports' in request.endpoint }}" href="{{ url_for('reports.index') }}">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint and 'notifications' in request.endpoint }}" href="{{ url_for('notifications.index') }}">
                                <i class="fas fa-bell"></i>
                                الإشعارات
                                <span id="notificationCount" class="badge bg-danger ms-1 d-none">0</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showImportModal()">
                                <i class="fas fa-file-excel"></i>
                                استيراد من Excel
                            </a>
                        </li>
                        {% if current_user.is_authenticated and current_user.is_admin %}
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint and 'backup' in request.endpoint }}" href="{{ url_for('backup.index') }}">
                                <i class="fas fa-shield-alt"></i>
                                النسخ الاحتياطي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Import Modal -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">استيراد البيانات من Excel</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="importForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="excelFile" class="form-label">اختر ملف Excel</label>
                            <input type="file" class="form-control" id="excelFile" accept=".xlsx,.xls" required>
                            <div class="form-text">
                                يجب أن يحتوي الملف على الأعمدة: رقم الصندوق، اسم المشترك، رقم الهاتف، العنوان، رقم الهوية/الجواز، حجم الصندوق، تاريخ البدء، تاريخ الانتهاء
                                <br>
                                <a href="/import/template" class="btn btn-sm btn-outline-info mt-2">
                                    <i class="fas fa-download"></i> تحميل قالب Excel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="importData()">استيراد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global search functionality
        let searchTimeout;
        document.getElementById('globalSearch').addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length < 2) {
                document.getElementById('searchResults').classList.add('d-none');
                return;
            }
            
            searchTimeout = setTimeout(() => {
                fetch(`/search?q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        displaySearchResults(data.results);
                    })
                    .catch(error => console.error('Search error:', error));
            }, 300);
        });
        
        function displaySearchResults(results) {
            const resultsDiv = document.getElementById('searchResults');
            
            if (results.length === 0) {
                resultsDiv.classList.add('d-none');
                return;
            }
            
            resultsDiv.innerHTML = results.map(result => `
                <div class="search-result-item" onclick="window.location.href='${result.url}'">
                    <div class="fw-bold">${result.name}</div>
                    <div class="text-muted Small">
                        صندوق رقم: ${result.box_number} | الهاتف: ${result.phone}
                        <span class="badge bg-secondary ms-2">${result.status}</span>
                    </div>
                </div>
            `).join('');
            
            resultsDiv.classList.remove('d-none');
        }
        
        // Hide search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.search-box')) {
                document.getElementById('searchResults').classList.add('d-none');
            }
        });
        
        // Import modal functions
        function showImportModal() {
            new bootstrap.Modal(document.getElementById('importModal')).show();
        }
        
        function importData() {
            const fileInput = document.getElementById('excelFile');
            const file = fileInput.files[0];

            if (!file) {
                alert('يرجى اختيار ملف Excel');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            // Show loading
            const modal = bootstrap.Modal.getInstance(document.getElementById('importModal'));
            const submitBtn = document.querySelector('#importModal .btn-primary');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاستيراد...';
            submitBtn.disabled = true;

            fetch('/import/excel', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`تم استيراد البيانات بنجاح\n${data.message}\nالناجح: ${data.results.successful}\nالفاشل: ${data.results.failed}`);
                    modal.hide();
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Import error:', error);
                alert('حدث خطأ أثناء الاستيراد');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        }
        
        // Notifications
        function showNotifications() {
            // This will be implemented later
            alert('سيتم تطوير نظام الإشعارات قريباً');
        }
        
        // Load notification count
        function loadNotificationCount() {
            fetch('/api/dashboard-data')
                .then(response => response.json())
                .then(data => {
                    const count = data.expiring_subscriptions ? data.expiring_subscriptions.length : 0;
                    document.getElementById('notificationCount').textContent = count;
                    if (count > 0) {
                        document.getElementById('notificationCount').classList.remove('d-none');
                    }
                })
                .catch(error => console.error('Notification count error:', error));
        }
        
        // Load notification count on page load
        document.addEventListener('DOMContentLoaded', loadNotificationCount);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
