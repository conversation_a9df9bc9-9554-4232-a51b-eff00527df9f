{% extends "base.html" %}

{% block title %}المشتركون - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">المشتركون</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('subscribers.add') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة مشترك جديد
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="الاسم، رقم الصندوق، رقم الهوية...">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">حالة الاشتراك</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" {{ 'selected' if status_filter == 'all' }}>الكل</option>
                    <option value="active" {{ 'selected' if status_filter == 'active' }}>نشط</option>
                    <option value="expired" {{ 'selected' if status_filter == 'expired' }}>منتهي</option>
                    <option value="expiring" {{ 'selected' if status_filter == 'expiring' }}>ينتهي قريباً</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="box_size" class="form-label">حجم الصندوق</label>
                <select class="form-select" id="box_size" name="box_size">
                    <option value="all" {{ 'selected' if box_size_filter == 'all' }}>الكل</option>
                    {% for size in box_sizes %}
                        <option value="{{ size }}" {{ 'selected' if box_size_filter == size }}>{{ size }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="per_page" class="form-label">عدد النتائج</label>
                <select class="form-select" id="per_page" name="per_page">
                    <option value="20" {{ 'selected' if request.args.get('per_page', '20') == '20' }}>20</option>
                    <option value="50" {{ 'selected' if request.args.get('per_page', '20') == '50' }}>50</option>
                    <option value="100" {{ 'selected' if request.args.get('per_page', '20') == '100' }}>100</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search"></i> بحث
                </button>
                <a href="{{ url_for('subscribers.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> مسح
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Results -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة المشتركين ({{ subscribers.total }} مشترك)</h5>
        <div>
            <a href="{{ url_for('reports.export_excel', type='subscribers') }}" class="btn btn-success btn-sm">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </a>
        </div>
    </div>
    <div class="card-body">
        {% if subscribers.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الصندوق</th>
                            <th>اسم المشترك</th>
                            <th>رقم الهاتف</th>
                            <th>حجم الصندوق</th>
                            <th>حالة الاشتراك</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for subscriber in subscribers.items %}
                            <tr>
                                <td>
                                    <strong>{{ subscriber.box_number }}</strong>
                                </td>
                                <td>{{ subscriber.name }}</td>
                                <td>
                                    <a href="tel:{{ subscriber.phone }}" class="text-decoration-none">
                                        {{ subscriber.phone }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ subscriber.box_size | box_size_arabic }}</span>
                                </td>
                                <td>
                                    {% set status = subscriber.subscription_status %}
                                    {% if status == 'Active' %}
                                        <span class="badge bg-success">{{ status | status_arabic }}</span>
                                    {% elif status == 'Expired' %}
                                        <span class="badge bg-danger">{{ status | status_arabic }}</span>
                                    {% elif status == 'Expiring Soon' %}
                                        <span class="badge bg-warning">{{ status | status_arabic }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ status | status_arabic }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if subscriber.current_subscription %}
                                        {{ subscriber.current_subscription.end_date.strftime('%Y-%m-%d') }}
                                        {% if subscriber.current_subscription.days_remaining <= 15 %}
                                            <Small class="text-muted">
                                                ({{ subscriber.current_subscription.days_remaining }} يوم)
                                            </Small>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">لا يوجد اشتراك</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('subscribers.view', id=subscriber.id) }}" 
                                           class="btn btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('subscribers.edit', id=subscriber.id) }}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteSubscriber({{ subscriber.id }})" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if subscribers.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if subscribers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('subscribers.index', page=subscribers.prev_num, search=search, status=status_filter, box_size=box_size_filter, per_page=request.args.get('per_page', 20)) }}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}

                        {% for page_num in subscribers.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != subscribers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('subscribers.index', page=page_num, search=search, status=status_filter, box_size=box_size_filter, per_page=request.args.get('per_page', 20)) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if subscribers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('subscribers.index', page=subscribers.next_num, search=search, status=status_filter, box_size=box_size_filter, per_page=request.args.get('per_page', 20)) }}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد نتائج</h5>
                <p class="text-muted">لم يتم العثور على مشتركين يطابقون معايير البحث</p>
                <a href="{{ url_for('subscribers.add') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة مشترك جديد
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function deleteSubscriber(id) {
    if (confirm('هل أنت متأكد من حذف هذا المشترك؟ سيتم حذف جميع اشتراكاته أيضاً.')) {
        fetch(`/subscribers/${id}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Delete error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
}

// Auto-submit form on filter change
document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('box_size').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('per_page').addEventListener('change', function() {
    this.form.submit();
});
</script>
{% endblock %}
