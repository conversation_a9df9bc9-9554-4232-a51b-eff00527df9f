# -*- mode: python ; coding: utf-8 -*-
"""
ملف بناء النسخة المرئية (GUI) من نظام إدارة الصناديق البريدية
بدون نافذة console - للمستخدمين العاديين
"""

import os

a = Analysis(
    ['launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('app', 'app'),
        ('instance', 'instance'),
        ('config.py', '.'),
        ('icon.ico', '.') if os.path.exists('icon.ico') else None
    ],
    hiddenimports=[
        'flask',
        'flask_sqlalchemy',
        'flask_login',
        'pandas',
        'openpyxl',
        'werkzeug',
        'apscheduler',
        'psutil',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

# تصفية البيانات لإزالة القيم الفارغة
a.datas = [item for item in a.datas if item is not None]

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='PostalBoxManager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # بدون نافذة console
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)
