# -*- coding: utf-8 -*-
"""
Subscribers routes
"""

from flask import render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required
from app.subscribers import bp
from app.models import Subscriber, Subscription
from app.extensions import db
from datetime import date, datetime
from sqlalchemy import or_

@bp.route('/')
@login_required
def index():
    """List all subscribers with pagination and filtering"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '').strip()
    status_filter = request.args.get('status', 'all')
    box_size_filter = request.args.get('box_size', 'all')
    
    # Base query
    query = Subscriber.query.filter_by(is_active=True)
    
    # Apply search filter
    if search:
        query = query.filter(
            or_(
                Subscriber.name.contains(search),
                Subscriber.box_number.contains(search),
                Subscriber.passport_number.contains(search),
                Subscriber.phone.contains(search)
            )
        )
    
    # Apply box size filter
    if box_size_filter != 'all':
        query = query.filter_by(box_size=box_size_filter)
    
    # Apply status filter
    if status_filter != 'all':
        if status_filter == 'active':
            # Has active subscription
            query = query.join(Subscription).filter(
                Subscription.end_date >= date.today(),
                Subscription.is_active == True
            )
        elif status_filter == 'expired':
            # Has expired subscription
            query = query.join(Subscription).filter(
                Subscription.end_date < date.today(),
                Subscription.is_active == True
            )
        elif status_filter == 'expiring':
            # Expiring within 15 days
            from datetime import timedelta
            query = query.join(Subscription).filter(
                Subscription.end_date <= date.today() + timedelta(days=15),
                Subscription.end_date >= date.today(),
                Subscription.is_active == True
            )
    
    # Paginate results
    subscribers = query.order_by(Subscriber.name).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Get unique box sizes for filter dropdown
    box_sizes = db.session.query(Subscriber.box_size).distinct().all()
    box_sizes = [size[0] for size in box_sizes]
    
    return render_template('subscribers/index.html', 
                         subscribers=subscribers, 
                         search=search,
                         status_filter=status_filter,
                         box_size_filter=box_size_filter,
                         box_sizes=box_sizes)

@bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """Add new subscriber"""
    if request.method == 'POST':
        try:
            # Get form data
            data = request.get_json() if request.is_json else request.form
            
            # Check if box number already exists
            existing_box = Subscriber.query.filter_by(box_number=data['box_number']).first()
            if existing_box:
                return jsonify({'error': 'رقم الصندوق موجود مسبقاً'}), 400
            
            # Check if passport number already exists
            existing_passport = Subscriber.query.filter_by(passport_number=data['passport_number']).first()
            if existing_passport:
                return jsonify({'error': 'رقم الهوية/الجواز موجود مسبقاً'}), 400
            
            # Create new subscriber
            subscriber = Subscriber(
                box_number=data['box_number'],
                name=data['name'],
                phone=data['phone'],
                address=data.get('address', ''),
                passport_number=data['passport_number'],
                box_size=data['box_size'],
                email=data.get('email', '')
            )
            
            db.session.add(subscriber)
            db.session.commit()
            
            if request.is_json:
                return jsonify({'success': True, 'id': subscriber.id})
            else:
                flash('تم إضافة المشترك بنجاح', 'success')
                return redirect(url_for('subscribers.view', id=subscriber.id))
                
        except Exception as e:
            db.session.rollback()
            if request.is_json:
                return jsonify({'error': str(e)}), 500
            else:
                flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('subscribers/add.html')

@bp.route('/<int:id>')
@login_required
def view(id):
    """View subscriber details"""
    subscriber = Subscriber.query.get_or_404(id)
    
    # Get all subscriptions for this subscriber
    subscriptions = Subscription.query.filter_by(subscriber_id=id).order_by(
        Subscription.start_date.desc()
    ).all()
    
    return render_template('subscribers/view.html', 
                         subscriber=subscriber, 
                         subscriptions=subscriptions)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """Edit subscriber"""
    subscriber = Subscriber.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form
            
            # Check if box number already exists (excluding current subscriber)
            existing_box = Subscriber.query.filter(
                Subscriber.box_number == data['box_number'],
                Subscriber.id != id
            ).first()
            if existing_box:
                return jsonify({'error': 'رقم الصندوق موجود مسبقاً'}), 400
            
            # Check if passport number already exists (excluding current subscriber)
            existing_passport = Subscriber.query.filter(
                Subscriber.passport_number == data['passport_number'],
                Subscriber.id != id
            ).first()
            if existing_passport:
                return jsonify({'error': 'رقم الهوية/الجواز موجود مسبقاً'}), 400
            
            # Update subscriber data
            subscriber.box_number = data['box_number']
            subscriber.name = data['name']
            subscriber.phone = data['phone']
            subscriber.address = data.get('address', '')
            subscriber.passport_number = data['passport_number']
            subscriber.box_size = data['box_size']
            subscriber.email = data.get('email', '')
            subscriber.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            if request.is_json:
                return jsonify({'success': True})
            else:
                flash('تم تحديث بيانات المشترك بنجاح', 'success')
                return redirect(url_for('subscribers.view', id=id))
                
        except Exception as e:
            db.session.rollback()
            if request.is_json:
                return jsonify({'error': str(e)}), 500
            else:
                flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('subscribers/edit.html', subscriber=subscriber)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """Delete subscriber (soft delete)"""
    subscriber = Subscriber.query.get_or_404(id)

    try:
        # Soft delete - mark as inactive
        subscriber.is_active = False
        subscriber.updated_at = datetime.utcnow()

        # Also deactivate all subscriptions
        for subscription in subscriber.subscriptions:
            subscription.is_active = False

        db.session.commit()

        if request.is_json:
            return jsonify({'success': True})
        else:
            flash('تم حذف المشترك بنجاح', 'success')
            return redirect(url_for('subscribers.index'))

    except Exception as e:
        db.session.rollback()
        if request.is_json:
            return jsonify({'error': str(e)}), 500
        else:
            flash(f'حدث خطأ: {str(e)}', 'error')
            return redirect(url_for('subscribers.view', id=id))

@bp.route('/<int:id>/add-subscription', methods=['POST'])
@login_required
def add_subscription(id):
    """Add new subscription for subscriber"""
    subscriber = Subscriber.query.get_or_404(id)

    try:
        data = request.get_json() if request.is_json else request.form

        # Deactivate current subscription if exists
        current_sub = subscriber.current_subscription
        if current_sub:
            current_sub.is_active = False

        # Create new subscription
        subscription = Subscription(
            subscriber_id=id,
            start_date=datetime.strptime(data['start_date'], '%Y-%m-%d').date(),
            end_date=datetime.strptime(data['end_date'], '%Y-%m-%d').date(),
            amount_paid=float(data.get('amount_paid', 0)),
            payment_date=datetime.strptime(data['payment_date'], '%Y-%m-%d').date() if data.get('payment_date') else None,
            notes=data.get('notes', ''),
            is_active=True
        )

        db.session.add(subscription)
        db.session.commit()

        if request.is_json:
            return jsonify({'success': True, 'id': subscription.id})
        else:
            flash('تم إضافة الاشتراك بنجاح', 'success')
            return redirect(url_for('subscribers.view', id=id))

    except Exception as e:
        db.session.rollback()
        if request.is_json:
            return jsonify({'error': str(e)}), 500
        else:
            flash(f'حدث خطأ: {str(e)}', 'error')
            return redirect(url_for('subscribers.view', id=id))

@bp.route('/api/check-unique')
@login_required
def check_unique():
    """API endpoint to check if box number or passport number is unique"""
    field = request.args.get('field')
    value = request.args.get('value')
    exclude_id = request.args.get('exclude_id', type=int)

    if field == 'box_number':
        query = Subscriber.query.filter_by(box_number=value)
    elif field == 'passport_number':
        query = Subscriber.query.filter_by(passport_number=value)
    else:
        return jsonify({'error': 'Invalid field'}), 400

    if exclude_id:
        query = query.filter(Subscriber.id != exclude_id)

    exists = query.first() is not None
    return jsonify({'exists': exists})

@bp.route('/subscription/<int:subscription_id>/delete', methods=['POST'])
@login_required
def delete_subscription(subscription_id):
    """Delete a subscription"""
    try:
        subscription = Subscription.query.get_or_404(subscription_id)
        subscriber_id = subscription.subscriber_id

        # Don't allow deleting active subscriptions
        if subscription.is_active:
            return jsonify({'error': 'لا يمكن حذف الاشتراك النشط'}), 400

        db.session.delete(subscription)
        db.session.commit()

        if request.is_json:
            return jsonify({'success': True})
        else:
            flash('تم حذف الاشتراك بنجاح', 'success')
            return redirect(url_for('subscribers.view', id=subscriber_id))

    except Exception as e:
        db.session.rollback()
        if request.is_json:
            return jsonify({'error': str(e)}), 500
        else:
            flash(f'حدث خطأ: {str(e)}', 'error')
            return redirect(url_for('subscribers.view', id=subscriber_id))
