{% extends "base.html" %}

{% block title %}التقارير - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">التقارير</h1>
</div>

<div class="row">
    <!-- Financial Reports -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-chart-line fa-3x text-success"></i>
                </div>
                <h5 class="card-title">التقارير المالية</h5>
                <p class="card-text">عرض الإيرادات والمدفوعات والتحليلات المالية</p>
                <a href="{{ url_for('reports.financial') }}" class="btn btn-success">
                    <i class="fas fa-eye"></i> عرض التقرير
                </a>
            </div>
        </div>
    </div>

    <!-- Expiring Subscriptions -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-clock fa-3x text-warning"></i>
                </div>
                <h5 class="card-title">الاشتراكات المنتهية قريباً</h5>
                <p class="card-text">قائمة بالاشتراكات التي ستنتهي خلال فترة محددة</p>
                <a href="{{ url_for('reports.expiring') }}" class="btn btn-warning">
                    <i class="fas fa-eye"></i> عرض التقرير
                </a>
            </div>
        </div>
    </div>

    <!-- Expired Subscriptions -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-times-circle fa-3x text-danger"></i>
                </div>
                <h5 class="card-title">الاشتراكات المنتهية</h5>
                <p class="card-text">قائمة بالاشتراكات المنتهية الصلاحية</p>
                <a href="{{ url_for('reports.expired') }}" class="btn btn-danger">
                    <i class="fas fa-eye"></i> عرض التقرير
                </a>
            </div>
        </div>
    </div>

    <!-- Subscribers Report -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-users fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">تقرير المشتركين</h5>
                <p class="card-text">قائمة شاملة بجميع المشتركين وبياناتهم</p>
                <a href="{{ url_for('reports.export_excel', type='subscribers') }}" class="btn btn-primary">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </a>
            </div>
        </div>
    </div>

    <!-- Box Occupancy -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-mailbox fa-3x text-info"></i>
                </div>
                <h5 class="card-title">إشغال الصناديق</h5>
                <p class="card-text">تقرير عن حالة إشغال الصناديق حسب الحجم</p>
                <button class="btn btn-info" onclick="showBoxOccupancyReport()">
                    <i class="fas fa-eye"></i> عرض التقرير
                </button>
            </div>
        </div>
    </div>

    <!-- Custom Report -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-cog fa-3x text-secondary"></i>
                </div>
                <h5 class="card-title">تقرير مخصص</h5>
                <p class="card-text">إنشاء تقرير مخصص حسب معايير محددة</p>
                <button class="btn btn-secondary" onclick="showCustomReportModal()">
                    <i class="fas fa-plus"></i> إنشاء تقرير
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">إحصائيات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h3 class="text-primary" id="totalSubscribers">-</h3>
                            <p class="text-muted mb-0">إجمالي المشتركين</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h3 class="text-success" id="activeSubscriptions">-</h3>
                            <p class="text-muted mb-0">الاشتراكات النشطة</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h3 class="text-warning" id="expiringSoon">-</h3>
                            <p class="text-muted mb-0">ستنتهي قريباً</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <h3 class="text-danger" id="expired">-</h3>
                        <p class="text-muted mb-0">منتهية الصلاحية</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Box Occupancy Modal -->
<div class="modal fade" id="boxOccupancyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تقرير إشغال الصناديق</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="boxOccupancyContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Report Modal -->
<div class="modal fade" id="customReportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء تقرير مخصص</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customReportForm">
                    <div class="mb-3">
                        <label for="reportType" class="form-label">نوع التقرير</label>
                        <select class="form-select" id="reportType" required>
                            <option value="">اختر نوع التقرير</option>
                            <option value="subscribers">المشتركون</option>
                            <option value="subscriptions">الاشتراكات</option>
                            <option value="financial">مالي</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="dateFrom" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="dateFrom">
                    </div>
                    <div class="mb-3">
                        <label for="dateTo" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="dateTo">
                    </div>
                    <div class="mb-3">
                        <label for="boxSizeFilter" class="form-label">حجم الصندوق</label>
                        <select class="form-select" id="boxSizeFilter">
                            <option value="">الكل</option>
                            <option value="Small">Small</option>
                            <option value="Medium">Medium</option>
                            <option value="Large">Large</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="statusFilter" class="form-label">حالة الاشتراك</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">الكل</option>
                            <option value="active">نشط</option>
                            <option value="expired">منتهي</option>
                            <option value="expiring">ينتهي قريباً</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="generateCustomReport()">إنشاء التقرير</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Load quick stats
function loadQuickStats() {
    fetch('/api/dashboard-data')
        .then(response => response.json())
        .then(data => {
            // This would need to be implemented in the dashboard API
            document.getElementById('totalSubscribers').textContent = '0';
            document.getElementById('activeSubscriptions').textContent = '0';
            document.getElementById('expiringSoon').textContent = data.expiring_subscriptions ? data.expiring_subscriptions.length : '0';
            document.getElementById('expired').textContent = '0';
        })
        .catch(error => console.error('Stats error:', error));
}

// Show box occupancy report
function showBoxOccupancyReport() {
    const modal = new bootstrap.Modal(document.getElementById('boxOccupancyModal'));
    modal.show();
    
    // Load box occupancy data
    // This would need to be implemented as an API endpoint
    setTimeout(() => {
        document.getElementById('boxOccupancyContent').innerHTML = `
            <div class="row">
                <div class="col-md-4 text-center">
                    <h4 class="text-info">Small</h4>
                    <p>المشغول: 0 / الإجمالي: 0</p>
                    <div class="progress">
                        <div class="progress-bar bg-info" style="width: 0%"></div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <h4 class="text-warning">Medium</h4>
                    <p>المشغول: 0 / الإجمالي: 0</p>
                    <div class="progress">
                        <div class="progress-bar bg-warning" style="width: 0%"></div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <h4 class="text-success">Large</h4>
                    <p>المشغول: 0 / الإجمالي: 0</p>
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;
    }, 1000);
}

// Show custom report modal
function showCustomReportModal() {
    const modal = new bootstrap.Modal(document.getElementById('customReportModal'));
    modal.show();
    
    // Set default dates
    const today = new Date().toISOString().split('T')[0];
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    
    document.getElementById('dateFrom').value = lastMonth.toISOString().split('T')[0];
    document.getElementById('dateTo').value = today;
}

// Generate custom report
function generateCustomReport() {
    const form = document.getElementById('customReportForm');
    const formData = new FormData(form);
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value) params.append(key, value);
    }
    
    const reportType = document.getElementById('reportType').value;
    if (!reportType) {
        alert('يرجى اختيار نوع التقرير');
        return;
    }
    
    // Redirect to appropriate report page with filters
    let url = '';
    switch (reportType) {
        case 'subscribers':
            url = `/subscribers?${params.toString()}`;
            break;
        case 'subscriptions':
            url = `/reports/financial?${params.toString()}`;
            break;
        case 'financial':
            url = `/reports/financial?${params.toString()}`;
            break;
    }
    
    if (url) {
        window.open(url, '_blank');
    }
    
    bootstrap.Modal.getInstance(document.getElementById('customReportModal')).hide();
}

// Load stats on page load
document.addEventListener('DOMContentLoaded', loadQuickStats);
</script>
{% endblock %}
