# -*- coding: utf-8 -*-
"""
Notifications routes
"""

from flask import render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required
from app.notifications_bp import bp
from app.models import Subscriber, Subscription, Notification
from app.notifications import notification_service
from app.extensions import db
from datetime import date, timedelta
from sqlalchemy import desc

@bp.route('/')
@login_required
def index():
    """Notifications dashboard"""
    # Get recent notifications
    recent_notifications = Notification.query.join(Subscription).join(Subscriber).order_by(
        desc(Notification.sent_at)
    ).limit(50).all()
    
    # Get notification statistics
    today = date.today()
    stats = {
        'total_sent_today': Notification.query.filter(
            Notification.sent_at >= today
        ).count(),
        'successful_today': Notification.query.filter(
            Notification.sent_at >= today,
            Notification.is_successful == True
        ).count(),
        'failed_today': Notification.query.filter(
            Notification.sent_at >= today,
            Notification.is_successful == False
        ).count(),
        'pending_notifications': get_pending_notifications_count()
    }
    
    return render_template('notifications/index.html', 
                         recent_notifications=recent_notifications,
                         stats=stats)

@bp.route('/send-manual', methods=['POST'])
@login_required
def send_manual():
    """Send manual notification"""
    try:
        data = request.get_json()
        subscriber_id = data.get('subscriber_id')
        message = data.get('message')
        notification_type = data.get('type', 'email')
        
        if not all([subscriber_id, message]):
            return jsonify({'error': 'بيانات غير مكتملة'}), 400
        
        success, message_result = notification_service.send_manual_notification(
            subscriber_id, message, notification_type
        )
        
        if success:
            return jsonify({'success': True, 'message': message_result})
        else:
            return jsonify({'error': message_result}), 400
            
    except Exception as e:
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

@bp.route('/run-check', methods=['POST'])
@login_required
def run_check():
    """Manually run expiry notification check"""
    try:
        notifications_sent = notification_service.check_and_send_expiry_notifications()
        return jsonify({
            'success': True, 
            'message': f'تم إرسال {notifications_sent} إشعار'
        })
    except Exception as e:
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

@bp.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """Notification settings"""
    if request.method == 'POST':
        # This would handle notification settings updates
        # For now, just show a success message
        flash('تم حفظ الإعدادات بنجاح', 'success')
        return redirect(url_for('notifications.settings'))
    
    return render_template('notifications/settings.html')

@bp.route('/api/pending')
@login_required
def api_pending():
    """API endpoint for pending notifications"""
    pending = get_pending_notifications()
    return jsonify({
        'count': len(pending),
        'notifications': [
            {
                'subscriber_name': item['subscriber'].name,
                'box_number': item['subscriber'].box_number,
                'phone': item['subscriber'].phone,
                'email': item['subscriber'].email,
                'end_date': item['subscription'].end_date.strftime('%Y-%m-%d'),
                'days_remaining': item['subscription'].days_remaining,
                'subscription_id': item['subscription'].id
            }
            for item in pending
        ]
    })

@bp.route('/api/history')
@login_required
def api_history():
    """API endpoint for notification history"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    notifications = Notification.query.join(Subscription).join(Subscriber).order_by(
        desc(Notification.sent_at)
    ).paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        'notifications': [
            {
                'id': notif.id,
                'subscriber_name': notif.subscription.subscriber.name,
                'box_number': notif.subscription.subscriber.box_number,
                'type': notif.notification_type,
                'message': notif.message[:100] + '...' if len(notif.message) > 100 else notif.message,
                'sent_at': notif.sent_at.strftime('%Y-%m-%d %H:%M'),
                'is_successful': notif.is_successful,
                'error_message': notif.error_message,
                'days_before_expiry': notif.days_before_expiry
            }
            for notif in notifications.items
        ],
        'total': notifications.total,
        'pages': notifications.pages,
        'current_page': notifications.page,
        'has_next': notifications.has_next,
        'has_prev': notifications.has_prev
    })

def get_pending_notifications():
    """Get list of subscriptions that need notifications"""
    pending = []
    
    # Check for subscriptions expiring in 15, 7, 3, 1 days
    for days in [15, 7, 3, 1]:
        target_date = date.today() + timedelta(days=days)
        
        expiring_subscriptions = Subscription.query.join(Subscriber).filter(
            Subscription.end_date == target_date,
            Subscription.is_active == True,
            Subscriber.is_active == True
        ).all()
        
        for subscription in expiring_subscriptions:
            # Check if notification was already sent today
            existing_notification = Notification.query.filter_by(
                subscription_id=subscription.id,
                days_before_expiry=days
            ).filter(
                Notification.sent_at >= date.today()
            ).first()
            
            if not existing_notification:
                pending.append({
                    'subscription': subscription,
                    'subscriber': subscription.subscriber,
                    'days_remaining': days
                })
    
    return pending

def get_pending_notifications_count():
    """Get count of pending notifications"""
    return len(get_pending_notifications())
