# -*- coding: utf-8 -*-
"""
مسارات النسخ الاحتياطي
"""

import os
import json
from datetime import datetime
from flask import render_template, request, jsonify, flash, redirect, url_for, send_file, current_app
from flask_login import login_required, current_user
from app.backup_bp import bp
from app.backup import backup_system
from app.scheduler import backup_scheduler
from app.backup_monitor import backup_monitor


@bp.route('/')
@login_required
def index():
    """صفحة إدارة النسخ الاحتياطي الرئيسية"""
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('main.index'))
    
    # الحصول على قائمة النسخ الاحتياطية
    backups = backup_system.list_backups() if backup_system else []
    
    # الحصول على إعدادات النسخ الاحتياطي
    config = backup_system.config if backup_system else {}
    
    # حساب إحصائيات النسخ الاحتياطية
    total_backups = len(backups)
    total_size = sum(backup.get('size', 0) for backup in backups)
    
    # تحويل الحجم إلى وحدة مناسبة
    if total_size > 1024 * 1024 * 1024:  # GB
        total_size_str = f"{total_size / (1024 * 1024 * 1024):.2f} GB"
    elif total_size > 1024 * 1024:  # MB
        total_size_str = f"{total_size / (1024 * 1024):.2f} MB"
    else:  # KB
        total_size_str = f"{total_size / 1024:.2f} KB"
    
    # آخر نسخة احتياطية
    last_backup = backups[0] if backups else None
    
    return render_template('backup/index.html',
                         backups=backups,
                         config=config,
                         total_backups=total_backups,
                         total_size_str=total_size_str,
                         last_backup=last_backup)


@bp.route('/create', methods=['POST'])
@login_required
def create_backup():
    """إنشاء نسخة احتياطية جديدة"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403
    
    if not backup_system:
        return jsonify({'error': 'نظام النسخ الاحتياطي غير متاح'}), 500
    
    try:
        # الحصول على اسم النسخة الاحتياطية من الطلب
        data = request.get_json() if request.is_json else request.form
        backup_name = data.get('backup_name', '').strip()
        
        # إنشاء النسخة الاحتياطية
        result = backup_system.create_backup(backup_name if backup_name else None)
        
        if result['success']:
            if request.is_json:
                return jsonify({
                    'success': True,
                    'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
                    'backup_info': result['backup_info']
                })
            else:
                flash('تم إنشاء النسخة الاحتياطية بنجاح', 'success')
                return redirect(url_for('backup.index'))
        else:
            error_msg = result.get('error', 'حدث خطأ غير معروف')
            if request.is_json:
                return jsonify({'error': error_msg}), 500
            else:
                flash(f'خطأ في إنشاء النسخة الاحتياطية: {error_msg}', 'error')
                return redirect(url_for('backup.index'))
    
    except Exception as e:
        error_msg = f'حدث خطأ: {str(e)}'
        if request.is_json:
            return jsonify({'error': error_msg}), 500
        else:
            flash(error_msg, 'error')
            return redirect(url_for('backup.index'))


@bp.route('/restore', methods=['POST'])
@login_required
def restore_backup():
    """استعادة نسخة احتياطية"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403
    
    if not backup_system:
        return jsonify({'error': 'نظام النسخ الاحتياطي غير متاح'}), 500
    
    try:
        data = request.get_json() if request.is_json else request.form
        backup_path = data.get('backup_path', '').strip()
        
        if not backup_path:
            return jsonify({'error': 'مسار النسخة الاحتياطية مطلوب'}), 400
        
        # التحقق من أن المسار آمن (داخل مجلد النسخ الاحتياطي)
        backup_dir = os.path.abspath(backup_system.backup_dir)
        full_backup_path = os.path.abspath(backup_path)
        
        if not full_backup_path.startswith(backup_dir):
            return jsonify({'error': 'مسار النسخة الاحتياطية غير صالح'}), 400
        
        # استعادة النسخة الاحتياطية
        result = backup_system.restore_backup(backup_path)
        
        if result['success']:
            if request.is_json:
                return jsonify({
                    'success': True,
                    'message': result.get('message', 'تم استعادة النسخة الاحتياطية بنجاح')
                })
            else:
                flash('تم استعادة النسخة الاحتياطية بنجاح', 'success')
                return redirect(url_for('backup.index'))
        else:
            error_msg = result.get('error', 'حدث خطأ غير معروف')
            if request.is_json:
                return jsonify({'error': error_msg}), 500
            else:
                flash(f'خطأ في استعادة النسخة الاحتياطية: {error_msg}', 'error')
                return redirect(url_for('backup.index'))
    
    except Exception as e:
        error_msg = f'حدث خطأ: {str(e)}'
        if request.is_json:
            return jsonify({'error': error_msg}), 500
        else:
            flash(error_msg, 'error')
            return redirect(url_for('backup.index'))


@bp.route('/delete', methods=['POST'])
@login_required
def delete_backup():
    """حذف نسخة احتياطية"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403
    
    if not backup_system:
        return jsonify({'error': 'نظام النسخ الاحتياطي غير متاح'}), 500
    
    try:
        data = request.get_json() if request.is_json else request.form
        backup_path = data.get('backup_path', '').strip()
        
        if not backup_path:
            return jsonify({'error': 'مسار النسخة الاحتياطية مطلوب'}), 400
        
        # التحقق من أن المسار آمن (داخل مجلد النسخ الاحتياطي)
        backup_dir = os.path.abspath(backup_system.backup_dir)
        full_backup_path = os.path.abspath(backup_path)
        
        if not full_backup_path.startswith(backup_dir):
            return jsonify({'error': 'مسار النسخة الاحتياطية غير صالح'}), 400
        
        # حذف النسخة الاحتياطية
        result = backup_system.delete_backup(backup_path)
        
        if result['success']:
            if request.is_json:
                return jsonify({
                    'success': True,
                    'message': result.get('message', 'تم حذف النسخة الاحتياطية بنجاح')
                })
            else:
                flash('تم حذف النسخة الاحتياطية بنجاح', 'success')
                return redirect(url_for('backup.index'))
        else:
            error_msg = result.get('error', 'حدث خطأ غير معروف')
            if request.is_json:
                return jsonify({'error': error_msg}), 500
            else:
                flash(f'خطأ في حذف النسخة الاحتياطية: {error_msg}', 'error')
                return redirect(url_for('backup.index'))
    
    except Exception as e:
        error_msg = f'حدث خطأ: {str(e)}'
        if request.is_json:
            return jsonify({'error': error_msg}), 500
        else:
            flash(error_msg, 'error')
            return redirect(url_for('backup.index'))


@bp.route('/download/<path:backup_name>')
@login_required
def download_backup(backup_name):
    """تحميل نسخة احتياطية"""
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('main.index'))
    
    if not backup_system:
        flash('نظام النسخ الاحتياطي غير متاح', 'error')
        return redirect(url_for('backup.index'))
    
    try:
        # بناء مسار النسخة الاحتياطية
        backup_path = os.path.join(backup_system.backup_dir, backup_name)
        
        # التحقق من وجود الملف
        if not os.path.exists(backup_path):
            flash('النسخة الاحتياطية غير موجودة', 'error')
            return redirect(url_for('backup.index'))
        
        # التحقق من أن المسار آمن
        backup_dir = os.path.abspath(backup_system.backup_dir)
        full_backup_path = os.path.abspath(backup_path)
        
        if not full_backup_path.startswith(backup_dir):
            flash('مسار النسخة الاحتياطية غير صالح', 'error')
            return redirect(url_for('backup.index'))
        
        # إرسال الملف للتحميل
        return send_file(backup_path, as_attachment=True, download_name=backup_name)
    
    except Exception as e:
        flash(f'خطأ في تحميل النسخة الاحتياطية: {str(e)}', 'error')
        return redirect(url_for('backup.index'))


@bp.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """إعدادات النسخ الاحتياطي"""
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('main.index'))
    
    if not backup_system:
        flash('نظام النسخ الاحتياطي غير متاح', 'error')
        return redirect(url_for('backup.index'))
    
    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form
            
            # تحديث الإعدادات
            new_config = {}
            
            # إعدادات النسخ الاحتياطي
            if 'max_backups' in data:
                new_config['max_backups'] = int(data['max_backups'])
            
            if 'backup_interval_hours' in data:
                new_config['backup_interval_hours'] = int(data['backup_interval_hours'])
            
            if 'include_uploads' in data:
                new_config['include_uploads'] = data.get('include_uploads') in ['true', 'on', True]
            
            if 'compress_backup' in data:
                new_config['compress_backup'] = data.get('compress_backup') in ['true', 'on', True]
            
            if 'auto_backup_enabled' in data:
                new_config['auto_backup_enabled'] = data.get('auto_backup_enabled') in ['true', 'on', True]
            
            if 'backup_dir' in data:
                backup_dir = data['backup_dir'].strip()
                if backup_dir:
                    new_config['backup_dir'] = backup_dir
            
            # تحديث الإعدادات
            if backup_system.update_config(new_config):
                # تحديث الجدولة إذا تم تغيير إعدادات النسخ التلقائي
                if backup_scheduler and ('auto_backup_enabled' in new_config or 'backup_interval_hours' in new_config):
                    backup_scheduler.update_schedule()

                if request.is_json:
                    return jsonify({
                        'success': True,
                        'message': 'تم حفظ الإعدادات بنجاح'
                    })
                else:
                    flash('تم حفظ الإعدادات بنجاح', 'success')
            else:
                if request.is_json:
                    return jsonify({'error': 'خطأ في حفظ الإعدادات'}), 500
                else:
                    flash('خطأ في حفظ الإعدادات', 'error')
        
        except Exception as e:
            error_msg = f'حدث خطأ: {str(e)}'
            if request.is_json:
                return jsonify({'error': error_msg}), 500
            else:
                flash(error_msg, 'error')
    
    return render_template('backup/settings.html', config=backup_system.config)


@bp.route('/api/status')
@login_required
def api_status():
    """API للحصول على حالة النسخ الاحتياطي"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403
    
    if not backup_system:
        return jsonify({'error': 'نظام النسخ الاحتياطي غير متاح'}), 500
    
    try:
        # الحصول على معلومات النسخ الاحتياطية
        backups = backup_system.list_backups()
        config = backup_system.config
        
        # حساب الإحصائيات
        total_backups = len(backups)
        total_size = sum(backup.get('size', 0) for backup in backups)
        
        # آخر نسخة احتياطية
        last_backup = backups[0] if backups else None
        
        # التحقق مما إذا كان يجب إنشاء نسخة احتياطية
        should_backup = backup_system.should_backup_now()

        # الحصول على معلومات الجدولة
        scheduler_status = {}
        if backup_scheduler:
            scheduler_status = backup_scheduler.get_scheduler_status()

        return jsonify({
            'total_backups': total_backups,
            'total_size': total_size,
            'last_backup': last_backup,
            'should_backup': should_backup,
            'auto_backup_enabled': config.get('auto_backup_enabled', True),
            'backup_interval_hours': config.get('backup_interval_hours', 24),
            'max_backups': config.get('max_backups', 10),
            'scheduler_status': scheduler_status
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/api/check-backup')
@login_required
def api_check_backup():
    """API للتحقق من الحاجة لإنشاء نسخة احتياطية"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403
    
    if not backup_system:
        return jsonify({'error': 'نظام النسخ الاحتياطي غير متاح'}), 500
    
    try:
        should_backup = backup_system.should_backup_now()
        return jsonify({'should_backup': should_backup})

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/api/trigger-backup', methods=['POST'])
@login_required
def api_trigger_backup():
    """API لتشغيل النسخ الاحتياطي فوراً"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403

    if not backup_scheduler:
        return jsonify({'error': 'نظام الجدولة غير متاح'}), 500

    try:
        if backup_scheduler.trigger_backup_now():
            return jsonify({'success': True, 'message': 'تم تشغيل النسخ الاحتياطي'})
        else:
            return jsonify({'error': 'فشل في تشغيل النسخ الاحتياطي'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/api/update-schedule', methods=['POST'])
@login_required
def api_update_schedule():
    """API لتحديث جدولة النسخ الاحتياطي"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403

    if not backup_scheduler:
        return jsonify({'error': 'نظام الجدولة غير متاح'}), 500

    try:
        if backup_scheduler.update_schedule():
            return jsonify({'success': True, 'message': 'تم تحديث الجدولة'})
        else:
            return jsonify({'error': 'فشل في تحديث الجدولة'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/api/scheduler-status')
@login_required
def api_scheduler_status():
    """API للحصول على حالة المجدول"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403

    if not backup_scheduler:
        return jsonify({'error': 'نظام الجدولة غير متاح'}), 500

    try:
        status = backup_scheduler.get_scheduler_status()
        return jsonify(status)

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/monitor')
@login_required
def monitor():
    """صفحة مراقبة النسخ الاحتياطي"""
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('main.index'))

    # الحصول على تقرير الصحة
    health_report = backup_monitor.check_backup_health()

    # الحصول على التنبيهات النشطة
    active_alerts = backup_monitor.get_active_alerts()

    # الحصول على آخر التنبيهات
    recent_alerts = backup_monitor.get_all_alerts(20)

    return render_template('backup/monitor.html',
                         health_report=health_report,
                         active_alerts=active_alerts,
                         recent_alerts=recent_alerts)


@bp.route('/api/health-check')
@login_required
def api_health_check():
    """API لفحص صحة النسخ الاحتياطي"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403

    try:
        health_report = backup_monitor.check_backup_health()
        return jsonify(health_report)

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/api/alerts')
@login_required
def api_alerts():
    """API للحصول على التنبيهات"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403

    try:
        active_only = request.args.get('active_only', 'false').lower() == 'true'
        limit = int(request.args.get('limit', 50))

        if active_only:
            alerts = backup_monitor.get_active_alerts()
        else:
            alerts = backup_monitor.get_all_alerts(limit)

        return jsonify({'alerts': alerts})

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/api/resolve-alert', methods=['POST'])
@login_required
def api_resolve_alert():
    """API لحل تنبيه"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403

    try:
        data = request.get_json() if request.is_json else request.form
        alert_id = int(data.get('alert_id'))

        if backup_monitor.resolve_alert(alert_id):
            return jsonify({'success': True, 'message': 'تم حل التنبيه'})
        else:
            return jsonify({'error': 'التنبيه غير موجود'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/api/backup-report')
@login_required
def api_backup_report():
    """API للحصول على تقرير النسخ الاحتياطي"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403

    try:
        report = backup_monitor.generate_backup_report()
        return jsonify(report)

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/api/send-health-report', methods=['POST'])
@login_required
def api_send_health_report():
    """API لإرسال تقرير الصحة للمديرين"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403

    try:
        if backup_monitor.send_health_report_to_admins():
            return jsonify({'success': True, 'message': 'تم إرسال التقرير'})
        else:
            return jsonify({'error': 'فشل في إرسال التقرير'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500
