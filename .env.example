# Flask Configuration
FLASK_CONFIG=development
SECRET_KEY=your-secret-key-here-change-in-production

# Database Configuration
DATABASE_URL=sqlite:///postal_box_management.db

# Email Configuration (للإشعارات)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Redis Configuration (for Celery)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Backup Configuration
BACKUP_DIR=backups
MAX_BACKUPS=10
BACKUP_INTERVAL_HOURS=24

# Notification Configuration
NOTIFICATION_DAYS_BEFORE_EXPIRY=15
