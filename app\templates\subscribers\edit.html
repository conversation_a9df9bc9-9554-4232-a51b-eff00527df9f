{% extends "base.html" %}

{% block title %}تعديل المشترك - {{ subscriber.name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">تعديل المشترك</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('subscribers.view', id=subscriber.id) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i> العودة لعرض المشترك
            </a>
            <a href="{{ url_for('subscribers.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i> قائمة المشتركين
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">تعديل بيانات المشترك</h5>
            </div>
            <div class="card-body">
                <form id="subscriberForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="box_number" class="form-label">رقم الصندوق <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="box_number" name="box_number" value="{{ subscriber.box_number }}" required>
                            <div class="invalid-feedback" id="box_number_error"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم المشترك <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ subscriber.name }}" required>
                            <div class="invalid-feedback" id="name_error"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="phone" name="phone" value="{{ subscriber.phone }}" required>
                            <div class="invalid-feedback" id="phone_error"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="passport_number" class="form-label">رقم الهوية/الجواز <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="passport_number" name="passport_number" value="{{ subscriber.passport_number }}" required>
                            <div class="invalid-feedback" id="passport_number_error"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="box_size" class="form-label">حجم الصندوق <span class="text-danger">*</span></label>
                            <select class="form-select" id="box_size" name="box_size" required>
                                <option value="">اختر حجم الصندوق</option>
                                <option value="Small" {{ 'selected' if subscriber.box_size == 'Small' }}>صغير</option>
                                <option value="Medium" {{ 'selected' if subscriber.box_size == 'Medium' }}>متوسط</option>
                                <option value="Large" {{ 'selected' if subscriber.box_size == 'Large' }}>كبير</option>
                            </select>
                            <div class="invalid-feedback" id="box_size_error"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ subscriber.email or '' }}">
                            <div class="invalid-feedback" id="email_error"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3">{{ subscriber.address or '' }}</textarea>
                        <div class="invalid-feedback" id="address_error"></div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                        <button type="button" class="btn btn-danger" onclick="deleteSubscriber()">
                            <i class="fas fa-trash"></i> حذف المشترك
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Current Subscription Info -->
        {% if subscriber.current_subscription %}
        {% set current_sub = subscriber.current_subscription %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">الاشتراك الحالي</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>تاريخ البداية:</strong></td>
                        <td>{{ current_sub.start_date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الانتهاء:</strong></td>
                        <td>{{ current_sub.end_date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td><strong>المبلغ المدفوع:</strong></td>
                        <td>{{ current_sub.amount_paid | currency }}</td>
                    </tr>
                    <tr>
                        <td><strong>الحالة:</strong></td>
                        <td>
                            {% set status = subscriber.subscription_status %}
                            {% if status == 'Active' %}
                                <span class="badge bg-success">نشط</span>
                            {% elif status == 'Expiring Soon' %}
                                <span class="badge bg-warning">ينتهي قريباً</span>
                            {% elif status == 'Expired' %}
                                <span class="badge bg-danger">منتهي</span>
                            {% else %}
                                <span class="badge bg-secondary">لا يوجد اشتراك</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        {% endif %}
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('subscribers.view', id=subscriber.id) }}" class="btn btn-info">
                        <i class="fas fa-eye"></i> عرض تفاصيل المشترك
                    </a>
                    <button class="btn btn-primary" onclick="callSubscriber('{{ subscriber.phone }}')">
                        <i class="fas fa-phone"></i> اتصال
                    </button>
                    {% if subscriber.email %}
                    <button class="btn btn-secondary" onclick="emailSubscriber('{{ subscriber.email }}')">
                        <i class="fas fa-envelope"></i> إرسال بريد إلكتروني
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('subscriberForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Clear previous errors
    document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // Submit form
    fetch('{{ url_for("subscribers.edit", id=subscriber.id) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show';
            alert.innerHTML = `
                <i class="fas fa-check-circle"></i> تم تحديث بيانات المشترك بنجاح
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.card-body').insertBefore(alert, document.getElementById('subscriberForm'));
            
            // Redirect to view page after a short delay
            setTimeout(() => {
                window.location.href = '{{ url_for("subscribers.view", id=subscriber.id) }}';
            }, 1500);
        } else {
            // Show validation errors
            if (data.errors) {
                for (const [field, error] of Object.entries(data.errors)) {
                    const input = document.getElementById(field);
                    const errorDiv = document.getElementById(field + '_error');
                    if (input && errorDiv) {
                        input.classList.add('is-invalid');
                        errorDiv.textContent = error;
                    }
                }
            } else {
                alert('حدث خطأ: ' + (data.error || 'خطأ غير معروف'));
            }
        }
    })
    .catch(error => {
        console.error('Submit error:', error);
        alert('حدث خطأ أثناء حفظ البيانات');
    });
});

function callSubscriber(phone) {
    window.open(`tel:${phone}`);
}

function emailSubscriber(email) {
    window.open(`mailto:${email}`);
}

function deleteSubscriber() {
    if (confirm('هل أنت متأكد من حذف هذا المشترك؟ سيتم حذف جميع اشتراكاته أيضاً.')) {
        fetch('{{ url_for("subscribers.delete", id=subscriber.id) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف المشترك بنجاح');
                window.location.href = '{{ url_for("subscribers.index") }}';
            } else {
                alert('حدث خطأ: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Delete error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
}

// Auto-format phone number
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 0) {
        if (value.startsWith('966')) {
            value = '+' + value;
        } else if (value.startsWith('05')) {
            value = '+966' + value.substring(1);
        } else if (value.startsWith('5')) {
            value = '+966' + value;
        }
    }
    e.target.value = value;
});

// Validate box number uniqueness
document.getElementById('box_number').addEventListener('blur', function(e) {
    const boxNumber = e.target.value.trim();
    const currentBoxNumber = '{{ subscriber.box_number }}';
    
    if (boxNumber && boxNumber !== currentBoxNumber) {
        fetch(`/api/check-box-number?box_number=${encodeURIComponent(boxNumber)}`)
            .then(response => response.json())
            .then(data => {
                if (!data.available) {
                    e.target.classList.add('is-invalid');
                    document.getElementById('box_number_error').textContent = 'رقم الصندوق مستخدم بالفعل';
                } else {
                    e.target.classList.remove('is-invalid');
                    document.getElementById('box_number_error').textContent = '';
                }
            })
            .catch(error => console.error('Box number check error:', error));
    }
});
</script>
{% endblock %}
