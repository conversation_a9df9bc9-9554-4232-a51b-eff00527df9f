# نظام النسخ الاحتياطي - نظام إدارة الصناديق البريدية

## نظرة عامة

تم تطوير نظام شامل للنسخ الاحتياطي يتضمن النسخ التلقائي والمراقبة المستمرة لضمان أمان البيانات.

## المكونات الرئيسية

### 1. نظام النسخ الاحتياطي الأساسي (`app/backup.py`)
- **إنشاء النسخ الاحتياطية**: نسخ قاعدة البيانات والملفات المهمة
- **ضغط البيانات**: ضغط النسخ لتوفير مساحة التخزين
- **إدارة النسخ**: حذف النسخ القديمة تلقائياً
- **استعادة البيانات**: استعادة النسخ الاحتياطية بسهولة

### 2. نظام الجدولة (`app/scheduler.py`)
- **النسخ التلقائي**: جدولة النسخ الاحتياطي حسب فترات محددة
- **مراقبة المهام**: تتبع حالة المهام المجدولة
- **إشعارات النجاح/الفشل**: إرسال تقارير للمديرين

### 3. نظام المراقبة (`app/backup_monitor.py`)
- **فحص الصحة**: مراقبة حالة النظام باستمرار
- **التنبيهات**: إنشاء تنبيهات للمشاكل والتحذيرات
- **التقارير**: إنشاء تقارير شاملة عن حالة النسخ الاحتياطي

### 4. واجهة المستخدم (`app/backup_bp/`)
- **لوحة التحكم**: إدارة النسخ الاحتياطية من الواجهة
- **الإعدادات**: تخصيص إعدادات النسخ والجدولة
- **المراقبة**: عرض حالة النظام والتنبيهات

## الميزات الرئيسية

### ✅ النسخ الاحتياطي
- نسخ قاعدة البيانات SQLite بأمان
- نسخ ملفات التحميل والمرفقات
- ضغط النسخ الاحتياطية (ZIP)
- تسمية تلقائية بالتاريخ والوقت
- حفظ معلومات النسخة (metadata)

### ✅ النسخ التلقائي
- جدولة النسخ حسب فترات محددة (ساعات/أيام)
- تشغيل في الخلفية باستخدام APScheduler
- إعادة المحاولة في حالة الفشل
- إشعارات البريد الإلكتروني

### ✅ إدارة النسخ
- عرض قائمة النسخ الاحتياطية
- تحميل النسخ الاحتياطية
- حذف النسخ القديمة تلقائياً
- استعادة النسخ الاحتياطية

### ✅ المراقبة والتنبيهات
- فحص صحة النظام
- مراقبة مساحة التخزين
- تنبيهات المشاكل الحرجة
- تقارير دورية للمديرين

### ✅ الأمان
- التحقق من صلاحيات المدير
- حماية مسارات الملفات
- تشفير كلمات المرور في الإعدادات
- سجلات مفصلة للعمليات

## التثبيت والإعداد

### المتطلبات
```bash
pip install APScheduler>=3.10.0 psutil>=5.9.0
```

### التهيئة
النظام يتم تهيئته تلقائياً عند بدء التطبيق في `app/__init__.py`:

```python
# Initialize backup system
from app.backup import init_app as init_backup
init_backup(app)

# Initialize backup scheduler
from app.scheduler import init_scheduler
init_scheduler(app)
```

## الاستخدام

### الوصول للنظام
- **الرابط**: `/backup/`
- **المطلوب**: تسجيل دخول كمدير
- **الصفحات**:
  - `/backup/` - لوحة التحكم الرئيسية
  - `/backup/settings` - إعدادات النسخ الاحتياطي
  - `/backup/monitor` - مراقبة النظام

### إنشاء نسخة احتياطية يدوياً
1. انتقل إلى صفحة النسخ الاحتياطي
2. اضغط على "إنشاء نسخة احتياطية جديدة"
3. أدخل اسم النسخة (اختياري)
4. اضغط "إنشاء النسخة"

### تكوين النسخ التلقائي
1. انتقل إلى "إعدادات النسخ الاحتياطي"
2. فعّل "النسخ التلقائي"
3. حدد فترة النسخ (بالساعات)
4. احفظ الإعدادات

### استعادة نسخة احتياطية
1. انتقل إلى قائمة النسخ الاحتياطية
2. اضغط على زر "استعادة" للنسخة المطلوبة
3. أكد العملية

## الإعدادات

### الإعدادات الافتراضية
```json
{
    "backup_dir": "backups",
    "max_backups": 10,
    "backup_interval_hours": 24,
    "include_uploads": true,
    "compress_backup": true,
    "auto_backup_enabled": true
}
```

### تخصيص الإعدادات
- **backup_dir**: مجلد حفظ النسخ الاحتياطية
- **max_backups**: عدد النسخ المحفوظة (الأقدم يُحذف تلقائياً)
- **backup_interval_hours**: فترة النسخ التلقائي بالساعات
- **include_uploads**: تضمين ملفات التحميل
- **compress_backup**: ضغط النسخ الاحتياطية
- **auto_backup_enabled**: تفعيل النسخ التلقائي

## API المتاح

### إنشاء نسخة احتياطية
```
POST /backup/create
Content-Type: application/json
{
    "backup_name": "اسم_النسخة" // اختياري
}
```

### استعادة نسخة احتياطية
```
POST /backup/restore
Content-Type: application/json
{
    "backup_path": "مسار_النسخة"
}
```

### حالة النظام
```
GET /backup/api/status
```

### فحص الصحة
```
GET /backup/api/health-check
```

## المراقبة والتنبيهات

### أنواع التنبيهات
- **حرجة**: مشاكل تتطلب تدخل فوري
- **تحذيرات**: مشاكل قد تؤثر على الأداء
- **معلومات**: إشعارات عامة

### فحوصات الصحة
- وجود مجلد النسخ الاحتياطي
- مساحة التخزين المتاحة
- حالة قاعدة البيانات
- حالة نظام الجدولة
- آخر نسخة احتياطية

## الأمان والنصائح

### أفضل الممارسات
1. **النسخ المتعددة**: احتفظ بنسخ في مواقع متعددة
2. **الاختبار الدوري**: اختبر استعادة النسخ بانتظام
3. **المراقبة**: راقب التنبيهات والتقارير
4. **التحديث**: حدّث الإعدادات حسب الحاجة

### التحذيرات
- تأكد من وجود مساحة كافية للنسخ الاحتياطية
- لا تحذف النسخ الاحتياطية يدوياً من مجلد النظام
- اختبر عملية الاستعادة قبل الاعتماد عليها
- احتفظ بنسخ خارجية للبيانات المهمة

## استكشاف الأخطاء

### مشاكل شائعة
1. **فشل النسخ التلقائي**: تحقق من حالة المجدول
2. **مساحة التخزين**: تحقق من المساحة المتاحة
3. **صلاحيات الملفات**: تأكد من صلاحيات الكتابة
4. **قاعدة البيانات مقفلة**: أغلق الاتصالات النشطة

### السجلات
- سجلات النظام: `backup.log`
- سجلات المراقبة: `backup_monitor.json`
- إعدادات النسخ: `backups/backup_config.json`

## الاختبار

تم إنشاء ملف اختبار شامل:
```bash
python simple_backup_test.py
```

يختبر الملف:
- تهيئة الأنظمة
- إنشاء النسخ الاحتياطية
- الجدولة والمراقبة
- الواجهات والAPI

## الدعم

للحصول على المساعدة:
1. راجع سجلات النظام
2. تحقق من صفحة المراقبة
3. اختبر النظام باستخدام ملف الاختبار
4. راجع هذا الدليل للحلول

---

تم تطوير هذا النظام لضمان أمان وسلامة بيانات نظام إدارة الصناديق البريدية مع إمكانيات متقدمة للمراقبة والإدارة.
