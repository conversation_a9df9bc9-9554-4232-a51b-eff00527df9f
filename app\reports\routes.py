# -*- coding: utf-8 -*-
"""
Reports routes
"""

from flask import render_template, request, jsonify, send_file, flash
from flask_login import login_required
from app.reports import bp
from app.models import Subscriber, Subscription, Notification
from app.extensions import db
from datetime import date, datetime, timedelta
from sqlalchemy import func, and_
import pandas as pd
import io
import os

@bp.route('/')
@login_required
def index():
    """Reports dashboard"""
    return render_template('reports/index.html')

@bp.route('/financial')
@login_required
def financial():
    """Financial reports"""
    # Get date range from request
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not start_date:
        start_date = (date.today() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = date.today().strftime('%Y-%m-%d')
    
    # Convert to date objects
    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # Get financial data
    subscriptions = Subscription.query.filter(
        and_(
            Subscription.payment_date >= start_date_obj,
            Subscription.payment_date <= end_date_obj
        )
    ).all()
    
    # Calculate totals
    total_revenue = sum(sub.amount_paid for sub in subscriptions)
    total_subscriptions = len(subscriptions)
    
    # Group by month
    monthly_revenue = {}
    for sub in subscriptions:
        month_key = sub.payment_date.strftime('%Y-%m') if sub.payment_date else 'Unknown'
        if month_key not in monthly_revenue:
            monthly_revenue[month_key] = {'revenue': 0, 'count': 0}
        monthly_revenue[month_key]['revenue'] += sub.amount_paid
        monthly_revenue[month_key]['count'] += 1
    
    # Group by box size
    box_size_revenue = {}
    size_map = {
        'Small': 'صغير',
        'Medium': 'متوسط',
        'Large': 'كبير'
    }

    for sub in subscriptions:
        box_size = sub.subscriber.box_size
        box_size_arabic = size_map.get(box_size, box_size)
        if box_size_arabic not in box_size_revenue:
            box_size_revenue[box_size_arabic] = {'revenue': 0, 'count': 0}
        box_size_revenue[box_size_arabic]['revenue'] += sub.amount_paid
        box_size_revenue[box_size_arabic]['count'] += 1
    
    return render_template('reports/financial.html',
                         start_date=start_date,
                         end_date=end_date,
                         total_revenue=total_revenue,
                         total_subscriptions=total_subscriptions,
                         monthly_revenue=monthly_revenue,
                         box_size_revenue=box_size_revenue,
                         subscriptions=subscriptions)

@bp.route('/expiring')
@login_required
def expiring():
    """Expiring subscriptions report"""
    days_ahead = request.args.get('days', 15, type=int)
    
    # Get expiring subscriptions
    expiring_subscriptions = Subscription.query.join(Subscriber).filter(
        Subscription.end_date <= date.today() + timedelta(days=days_ahead),
        Subscription.end_date >= date.today(),
        Subscription.is_active == True,
        Subscriber.is_active == True
    ).order_by(Subscription.end_date).all()
    
    return render_template('reports/expiring.html',
                         expiring_subscriptions=expiring_subscriptions,
                         days_ahead=days_ahead)

@bp.route('/expired')
@login_required
def expired():
    """Expired subscriptions report"""
    # Get expired subscriptions
    expired_subscriptions = Subscription.query.join(Subscriber).filter(
        Subscription.end_date < date.today(),
        Subscription.is_active == True,
        Subscriber.is_active == True
    ).order_by(Subscription.end_date.desc()).all()
    
    return render_template('reports/expired.html',
                         expired_subscriptions=expired_subscriptions)

@bp.route('/export/excel')
@login_required
def export_excel():
    """Export data to Excel"""
    export_type = request.args.get('type', 'subscribers')
    
    try:
        if export_type == 'subscribers':
            # Export subscribers data
            subscribers = Subscriber.query.filter_by(is_active=True).all()
            
            data = []
            for subscriber in subscribers:
                current_sub = subscriber.current_subscription
                data.append({
                    'رقم الصندوق': subscriber.box_number,
                    'اسم المشترك': subscriber.name,
                    'رقم الهاتف': subscriber.phone,
                    'العنوان': subscriber.address,
                    'رقم الهوية/الجواز': subscriber.passport_number,
                    'حجم الصندوق': subscriber.box_size_arabic,
                    'البريد الإلكتروني': subscriber.email,
                    'تاريخ البدء': current_sub.start_date.strftime('%Y-%m-%d') if current_sub else '',
                    'تاريخ الانتهاء': current_sub.end_date.strftime('%Y-%m-%d') if current_sub else '',
                    'المبلغ المدفوع': current_sub.amount_paid if current_sub else 0,
                    'حالة الاشتراك': subscriber.subscription_status_arabic
                })
            
            df = pd.DataFrame(data)
            filename = f'subscribers_{date.today().strftime("%Y%m%d")}.xlsx'
            
        elif export_type == 'financial':
            # Export financial data
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')
            
            if not start_date:
                start_date = (date.today() - timedelta(days=30)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = date.today().strftime('%Y-%m-%d')
            
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            subscriptions = Subscription.query.join(Subscriber).filter(
                and_(
                    Subscription.payment_date >= start_date_obj,
                    Subscription.payment_date <= end_date_obj
                ),
                Subscriber.is_active == True
            ).all()
            
            data = []
            for sub in subscriptions:
                data.append({
                    'رقم الصندوق': sub.subscriber.box_number,
                    'اسم المشترك': sub.subscriber.name,
                    'رقم الهاتف': sub.subscriber.phone,
                    'حجم الصندوق': sub.subscriber.box_size_arabic,
                    'تاريخ البدء': sub.start_date.strftime('%Y-%m-%d'),
                    'تاريخ الانتهاء': sub.end_date.strftime('%Y-%m-%d'),
                    'تاريخ الدفع': sub.payment_date.strftime('%Y-%m-%d') if sub.payment_date else '',
                    'المبلغ المدفوع': sub.amount_paid,
                    'ملاحظات': sub.notes
                })
            
            df = pd.DataFrame(data)
            filename = f'financial_report_{start_date}_{end_date}.xlsx'
            
        else:
            return jsonify({'error': 'نوع التصدير غير صحيح'}), 400
        
        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='البيانات', index=False)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['البيانات']
            
            # Add some formatting
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })
            
            # Write the column headers with the defined format
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)
            
            # Auto-adjust column widths
            for i, col in enumerate(df.columns):
                max_len = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, max_len)
        
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(request.referrer or url_for('reports.index'))
