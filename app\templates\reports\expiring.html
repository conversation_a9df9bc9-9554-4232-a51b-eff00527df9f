{% extends "base.html" %}

{% block title %}الاشتراكات المنتهية قريباً - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">الاشتراكات المنتهية قريباً</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> العودة للتقارير
            </a>
            <button class="btn btn-outline-primary" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
            <a href="{{ url_for('reports.export_excel', type='expiring') }}" class="btn btn-outline-success">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </a>
        </div>
    </div>
</div>

<!-- Filter Options -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="days" class="form-label">عدد الأيام القادمة</label>
                <select class="form-select" id="days" name="days" onchange="this.form.submit()">
                    <option value="7" {{ 'selected' if days_ahead == 7 }}>7 أيام</option>
                    <option value="15" {{ 'selected' if days_ahead == 15 }}>15 يوم</option>
                    <option value="30" {{ 'selected' if days_ahead == 30 }}>30 يوم</option>
                    <option value="60" {{ 'selected' if days_ahead == 60 }}>60 يوم</option>
                </select>
            </div>
            <div class="col-md-9 d-flex align-items-end">
                <div class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    عرض الاشتراكات التي ستنتهي خلال {{ days_ahead }} يوم القادمة
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            إجمالي الاشتراكات المنتهية قريباً
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ expiring_subscriptions|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            تنتهي خلال 7 أيام
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ expiring_subscriptions|selectattr('days_remaining', 'le', 7)|list|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            إجمالي الإيرادات المتوقعة
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ expiring_subscriptions|sum(attribute='amount_paid') | currency }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

</div>

<!-- Expiring Subscriptions Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            الاشتراكات المنتهية خلال {{ days_ahead }} يوم القادمة
        </h6>
    </div>
    <div class="card-body">
        {% if expiring_subscriptions %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>اسم المشترك</th>
                            <th>رقم الصندوق</th>
                            <th>حجم الصندوق</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الأيام المتبقية</th>
                            <th>المبلغ المدفوع</th>
                            <th>رقم الهاتف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for subscription in expiring_subscriptions %}
                            <tr class="{{ 'table-danger' if subscription.days_remaining <= 3 else 'table-warning' if subscription.days_remaining <= 7 else '' }}">
                                <td>
                                    <a href="{{ url_for('subscribers.view', id=subscription.subscriber.id) }}" class="text-decoration-none">
                                        {{ subscription.subscriber.name }}
                                    </a>
                                </td>
                                <td>
                                    <strong>{{ subscription.subscriber.box_number }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ subscription.subscriber.box_size | box_size_arabic }}</span>
                                </td>
                                <td>{{ subscription.end_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if subscription.days_remaining <= 3 else 'warning' if subscription.days_remaining <= 7 else 'primary' }}">
                                        {{ subscription.days_remaining }} يوم
                                    </span>
                                </td>
                                <td>{{ subscription.amount_paid | currency }}</td>
                                <td>
                                    {{ subscription.subscriber.phone }}
                                    <a href="tel:{{ subscription.subscriber.phone }}" class="btn btn-sm btn-outline-primary ms-1">
                                        <i class="fas fa-phone"></i>
                                    </a>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('subscribers.view', id=subscription.subscriber.id) }}" 
                                           class="btn btn-outline-primary" title="عرض المشترك">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-success" 
                                                onclick="renewSubscription({{ subscription.subscriber.id }})" 
                                                title="تجديد الاشتراك">
                                            <i class="fas fa-sync"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" 
                                                onclick="sendNotification('{{ subscription.subscriber.box_number }}')" 
                                                title="إرسال إشعار">
                                            <i class="fas fa-bell"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h5 class="text-muted">لا توجد اشتراكات منتهية قريباً</h5>
                <p class="text-muted">جميع الاشتراكات صالحة لأكثر من {{ days_ahead }} يوم.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function renewSubscription(subscriberId) {
    // Redirect to subscriber page to add new subscription
    window.location.href = `/subscribers/${subscriberId}`;
}

function sendNotification(boxNumber) {
    // This will be implemented with the notification system
    alert(`سيتم إرسال إشعار للصندوق رقم: ${boxNumber}`);
}

// Auto-refresh every 5 minutes
setTimeout(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
