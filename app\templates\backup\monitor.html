{% extends "base.html" %}

{% block title %}مراقبة النسخ الاحتياطي - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-heartbeat me-2"></i>مراقبة النسخ الاحتياطي</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('backup.index') }}">النسخ الاحتياطي</a></li>
                        <li class="breadcrumb-item active">المراقبة</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- حالة الصحة العامة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header 
                    {% if health_report.status == 'healthy' %}bg-success{% elif health_report.status == 'warning' %}bg-warning{% else %}bg-danger{% endif %} 
                    text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-heartbeat me-2"></i>حالة النظام العامة
                        <span class="badge bg-light text-dark ms-2">
                            {% if health_report.status == 'healthy' %}سليم{% elif health_report.status == 'warning' %}تحذير{% else %}خطر{% endif %}
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- المشاكل الحرجة -->
                        {% if health_report.issues %}
                        <div class="col-md-4">
                            <h6 class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>مشاكل حرجة</h6>
                            <ul class="list-unstyled">
                                {% for issue in health_report.issues %}
                                <li class="text-danger mb-1">
                                    <i class="fas fa-times me-1"></i>{{ issue }}
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <!-- التحذيرات -->
                        {% if health_report.warnings %}
                        <div class="col-md-4">
                            <h6 class="text-warning"><i class="fas fa-exclamation-triangle me-2"></i>تحذيرات</h6>
                            <ul class="list-unstyled">
                                {% for warning in health_report.warnings %}
                                <li class="text-warning mb-1">
                                    <i class="fas fa-exclamation-triangle me-1"></i>{{ warning }}
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <!-- معلومات إضافية -->
                        {% if health_report.info %}
                        <div class="col-md-4">
                            <h6 class="text-info"><i class="fas fa-info-circle me-2"></i>معلومات</h6>
                            <ul class="list-unstyled">
                                {% for info in health_report.info %}
                                <li class="text-info mb-1">
                                    <i class="fas fa-info me-1"></i>{{ info }}
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <!-- إذا لم تكن هناك مشاكل -->
                        {% if not health_report.issues and not health_report.warnings and not health_report.info %}
                        <div class="col-12 text-center">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-success">النظام يعمل بشكل طبيعي</h5>
                            <p class="text-muted">جميع أنظمة النسخ الاحتياطي تعمل بشكل صحيح</p>
                        </div>
                        {% endif %}
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="mt-3 text-end">
                        <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="refreshHealthCheck()">
                            <i class="fas fa-sync me-1"></i>تحديث الفحص
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm me-2" onclick="sendHealthReport()">
                            <i class="fas fa-paper-plane me-1"></i>إرسال التقرير
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="downloadReport()">
                            <i class="fas fa-download me-1"></i>تحميل التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التنبيهات النشطة -->
    {% if active_alerts %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell me-2"></i>التنبيهات النشطة
                        <span class="badge bg-dark">{{ active_alerts|length }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>الرسالة</th>
                                    <th>الخطورة</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for alert in active_alerts %}
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">{{ alert.type }}</span>
                                    </td>
                                    <td>{{ alert.message }}</td>
                                    <td>
                                        {% if alert.severity == 'critical' %}
                                            <span class="badge bg-danger">حرج</span>
                                        {% elif alert.severity == 'warning' %}
                                            <span class="badge bg-warning text-dark">تحذير</span>
                                        {% else %}
                                            <span class="badge bg-info">معلومات</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ alert.timestamp[:19].replace('T', ' ') }}</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                onclick="resolveAlert({{ alert.id }})">
                                            <i class="fas fa-check me-1"></i>حل
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- سجل التنبيهات -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>سجل التنبيهات
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_alerts %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>النوع</th>
                                        <th>الرسالة</th>
                                        <th>الخطورة</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for alert in recent_alerts %}
                                    <tr class="{% if not alert.get('resolved', False) %}table-warning{% endif %}">
                                        <td>
                                            <span class="badge bg-secondary">{{ alert.type }}</span>
                                        </td>
                                        <td>{{ alert.message }}</td>
                                        <td>
                                            {% if alert.severity == 'critical' %}
                                                <span class="badge bg-danger">حرج</span>
                                            {% elif alert.severity == 'warning' %}
                                                <span class="badge bg-warning text-dark">تحذير</span>
                                            {% else %}
                                                <span class="badge bg-info">معلومات</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ alert.timestamp[:19].replace('T', ' ') }}</td>
                                        <td>
                                            {% if alert.get('resolved', False) %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>محلول
                                                </span>
                                            {% else %}
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-exclamation me-1"></i>نشط
                                                </span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد تنبيهات</h5>
                            <p class="text-muted">لم يتم تسجيل أي تنبيهات بعد</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshHealthCheck() {
    showAlert('info', 'جاري تحديث فحص الصحة...');
    
    fetch('{{ url_for("backup.api_health_check") }}')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showAlert('danger', `خطأ: ${data.error}`);
            } else {
                showAlert('success', 'تم تحديث فحص الصحة');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'حدث خطأ في الاتصال');
        });
}

function sendHealthReport() {
    if (!confirm('هل تريد إرسال تقرير الصحة للمديرين؟')) {
        return;
    }
    
    fetch('{{ url_for("backup.api_send_health_report") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || 'تم إرسال التقرير');
        } else {
            showAlert('danger', data.error || 'فشل في إرسال التقرير');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في الاتصال');
    });
}

function downloadReport() {
    fetch('{{ url_for("backup.api_backup_report") }}')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showAlert('danger', `خطأ: ${data.error}`);
            } else {
                // تحويل التقرير إلى JSON وتحميله
                const dataStr = JSON.stringify(data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `backup_report_${new Date().toISOString().slice(0, 10)}.json`;
                link.click();
                URL.revokeObjectURL(url);
                
                showAlert('success', 'تم تحميل التقرير');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'حدث خطأ في الاتصال');
        });
}

function resolveAlert(alertId) {
    if (!confirm('هل تريد وضع علامة على هذا التنبيه كمحلول؟')) {
        return;
    }
    
    fetch('{{ url_for("backup.api_resolve_alert") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({alert_id: alertId})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || 'تم حل التنبيه');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.error || 'فشل في حل التنبيه');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في الاتصال');
    });
}

function showAlert(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert alert at the top of the container
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.children[1]);
}
</script>
{% endblock %}
