# -*- coding: utf-8 -*-
"""
Postal Box Management System
Application package initialization
"""

from flask import Flask
from config import config
import os

def create_app(config_name=None):
    """Application factory pattern"""
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')
    
    # Get the absolute path to the app directory
    app_dir = os.path.dirname(os.path.abspath(__file__))
    template_dir = os.path.join(app_dir, 'templates')

    app = Flask(__name__, template_folder=template_dir)
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    from app.extensions import db, migrate, login_manager
    db.init_app(app)
    login_manager.init_app(app)
    if migrate:
        migrate.init_app(app, db)

    # Initialize backup system
    from app.backup import init_app as init_backup
    init_backup(app)

    # Initialize backup scheduler
    from app.scheduler import init_scheduler
    init_scheduler(app)
    
    # Create upload directory if it doesn't exist
    upload_dir = os.path.join(os.getcwd(), app.config['UPLOAD_FOLDER'])
    os.makedirs(upload_dir, exist_ok=True)
    
    # Register blueprints
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)

    from app.subscribers import bp as subscribers_bp
    app.register_blueprint(subscribers_bp, url_prefix='/subscribers')

    from app.reports import bp as reports_bp
    app.register_blueprint(reports_bp, url_prefix='/reports')

    from app.import_export import bp as import_export_bp
    app.register_blueprint(import_export_bp, url_prefix='/import')

    from app.notifications_bp import bp as notifications_bp
    app.register_blueprint(notifications_bp, url_prefix='/notifications')

    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from app.backup_bp import bp as backup_bp
    app.register_blueprint(backup_bp, url_prefix='/backup')

    # Register template filters
    @app.template_filter('currency')
    def currency_filter(amount):
        """Format currency with 3 decimal places and Dinar suffix"""
        if amount is None:
            return "0.000 دينار"
        return f"{amount:,.3f} دينار"

    @app.template_filter('box_size_arabic')
    def box_size_arabic_filter(size):
        """Convert English box sizes to Arabic"""
        size_map = {
            'Small': 'صغير',
            'Medium': 'متوسط',
            'Large': 'كبير'
        }
        return size_map.get(size, size)

    @app.template_filter('status_arabic')
    def status_arabic_filter(status):
        """Convert English status to Arabic"""
        status_map = {
            'Active': 'نشط',
            'Expired': 'منتهي',
            'Expiring Soon': 'ينتهي قريباً',
            'No Subscription': 'لا يوجد اشتراك'
        }
        return status_map.get(status, status)

    return app
