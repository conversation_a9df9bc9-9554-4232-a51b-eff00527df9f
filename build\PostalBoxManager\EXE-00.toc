('D:\\\u200f\u200fPostal Box Management\\dist\\PostalBoxManager.exe',
 <PERSON><PERSON><PERSON>,
 <PERSON><PERSON><PERSON>,
 <PERSON>als<PERSON>,
 ['D:\\\u200f\u200fPostal Box Management\\icon.ico'],
 None,
 <PERSON>alse,
 <PERSON>alse,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'D:\\\u200f\u200fPostal Box '
 'Management\\build\\PostalBoxManager\\PostalBoxManager.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\\u200f\u200fPostal Box Management\\build\\PostalBoxManager\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\\u200f\u200fPostal Box '
   'Management\\build\\PostalBoxManager\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\\u200f\u200fPostal Box '
   'Management\\build\\PostalBoxManager\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\\u200f\u200fPostal Box '
   'Management\\build\\PostalBoxManager\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\\u200f\u200fPostal Box '
   'Management\\build\\PostalBoxManager\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\\u200f\u200fPostal Box '
   'Management\\build\\PostalBoxManager\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('launcher',
   'D:\\\u200f\u200fPostal Box Management\\launcher.py',
   'PYSOURCE'),
  ('python311.dll', 'C:\\Program Files\\Python311\\python311.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom311.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pywin32_system32\\pythoncom311.dll',
   'BINARY'),
  ('numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python311\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python311\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Program Files\\Python311\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python311\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python311\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32com\\shell\\shell.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_zoneinfo.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\greenlet\\_greenlet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('zope\\interface\\_zope_interface_coptimizations.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope\\interface\\_zope_interface_coptimizations.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_testinternalcapi.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_testinternalcapi.pyd',
   'EXTENSION'),
  ('_testcapi.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_testcapi.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('gevent\\resolver\\cares.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\resolver\\cares.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\libuv\\_corecffi.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\libuv\\_corecffi.pyd',
   'EXTENSION'),
  ('gevent\\libev\\corecext.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\libev\\corecext.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cqueue.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_cqueue.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_clocal.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_clocal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cgreenlet.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_cgreenlet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cevent.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_cevent.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_waiter.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_c_waiter.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_tracer.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_c_tracer.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_semaphore.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_c_semaphore.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_imap.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_c_imap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_ident.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_c_ident.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_hub_primitives.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_c_hub_primitives.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_hub_local.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_c_hub_local.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_greenlet_primitives.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_c_greenlet_primitives.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_abstract_linkable.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent\\_gevent_c_abstract_linkable.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\cyextension\\util.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\cyextension\\processors.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\sqlalchemy\\cyextension\\collections.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Program Files\\Python311\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('psycopg2\\_psycopg.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\psycopg2\\_psycopg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_mysql_connector.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\_mysql_connector.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\linalg\\lapack_lite.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\writers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\window\\indexers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\window\\aggregations.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\period.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\fields.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslibs\\base.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\tslib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\testing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\sparse.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\sas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\reshape.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\properties.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\parsers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\pandas_parser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\pandas_datetime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\ops_dispatch.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\ops.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\missing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\lib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\json.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\join.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\interval.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\internals.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\indexing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\index.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\hashtable.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\hashing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\groupby.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\byteswap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\arrays.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\_libs\\algos.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python311\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Program Files\\Python311\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Program Files\\Python311\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python311\\python3.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\Program Files\\Python311\\DLLs\\sqlite3.dll', 'BINARY'),
  ('pandas.libs\\msvcp140-fa0758dedafbbe194d3ee96e3dc2b9a3.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas.libs\\msvcp140-fa0758dedafbbe194d3ee96e3dc2b9a3.dll',
   'BINARY'),
  ('tcl86t.dll', 'C:\\Program Files\\Python311\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'C:\\Program Files\\Python311\\DLLs\\tk86t.dll', 'BINARY'),
  ('app\\__init__.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\__init__.py',
   'DATA'),
  ('app\\__pycache__\\__init__.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\__pycache__\\backup.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\__pycache__\\backup.cpython-311.pyc',
   'DATA'),
  ('app\\__pycache__\\backup_monitor.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\__pycache__\\backup_monitor.cpython-311.pyc',
   'DATA'),
  ('app\\__pycache__\\extensions.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\__pycache__\\extensions.cpython-311.pyc',
   'DATA'),
  ('app\\__pycache__\\models.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\__pycache__\\models.cpython-311.pyc',
   'DATA'),
  ('app\\__pycache__\\notifications.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\__pycache__\\notifications.cpython-311.pyc',
   'DATA'),
  ('app\\__pycache__\\scheduler.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\__pycache__\\scheduler.cpython-311.pyc',
   'DATA'),
  ('app\\auth\\__init__.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\auth\\__init__.py',
   'DATA'),
  ('app\\auth\\__pycache__\\__init__.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\auth\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\auth\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\auth\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('app\\auth\\__pycache__\\routes.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\auth\\__pycache__\\routes.cpython-311.pyc',
   'DATA'),
  ('app\\auth\\__pycache__\\routes.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\auth\\__pycache__\\routes.cpython-313.pyc',
   'DATA'),
  ('app\\auth\\routes.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\auth\\routes.py',
   'DATA'),
  ('app\\backup.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\backup.py',
   'DATA'),
  ('app\\backup_bp\\__init__.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\backup_bp\\__init__.py',
   'DATA'),
  ('app\\backup_bp\\__pycache__\\__init__.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\backup_bp\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\backup_bp\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\backup_bp\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('app\\backup_bp\\__pycache__\\routes.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\backup_bp\\__pycache__\\routes.cpython-311.pyc',
   'DATA'),
  ('app\\backup_bp\\__pycache__\\routes.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\backup_bp\\__pycache__\\routes.cpython-313.pyc',
   'DATA'),
  ('app\\backup_bp\\routes.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\backup_bp\\routes.py',
   'DATA'),
  ('app\\backup_monitor.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\backup_monitor.py',
   'DATA'),
  ('app\\extensions.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\extensions.py',
   'DATA'),
  ('app\\import_export\\__init__.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\import_export\\__init__.py',
   'DATA'),
  ('app\\import_export\\__pycache__\\__init__.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\import_export\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\import_export\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\import_export\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('app\\import_export\\__pycache__\\routes.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\import_export\\__pycache__\\routes.cpython-311.pyc',
   'DATA'),
  ('app\\import_export\\__pycache__\\routes.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\import_export\\__pycache__\\routes.cpython-313.pyc',
   'DATA'),
  ('app\\import_export\\routes.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\import_export\\routes.py',
   'DATA'),
  ('app\\main\\__init__.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\main\\__init__.py',
   'DATA'),
  ('app\\main\\__pycache__\\__init__.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\main\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\main\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\main\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('app\\main\\__pycache__\\routes.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\main\\__pycache__\\routes.cpython-311.pyc',
   'DATA'),
  ('app\\main\\__pycache__\\routes.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\main\\__pycache__\\routes.cpython-313.pyc',
   'DATA'),
  ('app\\main\\routes.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\main\\routes.py',
   'DATA'),
  ('app\\models.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\models.py',
   'DATA'),
  ('app\\notifications.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\notifications.py',
   'DATA'),
  ('app\\notifications_bp\\__init__.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\notifications_bp\\__init__.py',
   'DATA'),
  ('app\\notifications_bp\\__pycache__\\__init__.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\notifications_bp\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\notifications_bp\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\notifications_bp\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('app\\notifications_bp\\__pycache__\\routes.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\notifications_bp\\__pycache__\\routes.cpython-311.pyc',
   'DATA'),
  ('app\\notifications_bp\\__pycache__\\routes.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\notifications_bp\\__pycache__\\routes.cpython-313.pyc',
   'DATA'),
  ('app\\notifications_bp\\routes.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\notifications_bp\\routes.py',
   'DATA'),
  ('app\\reports\\__init__.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\reports\\__init__.py',
   'DATA'),
  ('app\\reports\\__pycache__\\__init__.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\reports\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\reports\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\reports\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('app\\reports\\__pycache__\\routes.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\reports\\__pycache__\\routes.cpython-311.pyc',
   'DATA'),
  ('app\\reports\\__pycache__\\routes.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\reports\\__pycache__\\routes.cpython-313.pyc',
   'DATA'),
  ('app\\reports\\routes.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\reports\\routes.py',
   'DATA'),
  ('app\\scheduler.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\scheduler.py',
   'DATA'),
  ('app\\static\\css\\.gitkeep',
   'D:\\\u200f\u200fPostal Box Management\\app\\static\\css\\.gitkeep',
   'DATA'),
  ('app\\static\\js\\.gitkeep',
   'D:\\\u200f\u200fPostal Box Management\\app\\static\\js\\.gitkeep',
   'DATA'),
  ('app\\subscribers\\__init__.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\subscribers\\__init__.py',
   'DATA'),
  ('app\\subscribers\\__pycache__\\__init__.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\subscribers\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\subscribers\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\subscribers\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('app\\subscribers\\__pycache__\\routes.cpython-311.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\subscribers\\__pycache__\\routes.cpython-311.pyc',
   'DATA'),
  ('app\\subscribers\\__pycache__\\routes.cpython-313.pyc',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\subscribers\\__pycache__\\routes.cpython-313.pyc',
   'DATA'),
  ('app\\subscribers\\routes.py',
   'D:\\\u200f\u200fPostal Box Management\\app\\subscribers\\routes.py',
   'DATA'),
  ('app\\templates\\auth\\change_password.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\auth\\change_password.html',
   'DATA'),
  ('app\\templates\\auth\\edit_user.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\auth\\edit_user.html',
   'DATA'),
  ('app\\templates\\auth\\login.html',
   'D:\\\u200f\u200fPostal Box Management\\app\\templates\\auth\\login.html',
   'DATA'),
  ('app\\templates\\auth\\profile.html',
   'D:\\\u200f\u200fPostal Box Management\\app\\templates\\auth\\profile.html',
   'DATA'),
  ('app\\templates\\auth\\register.html',
   'D:\\\u200f\u200fPostal Box Management\\app\\templates\\auth\\register.html',
   'DATA'),
  ('app\\templates\\auth\\users.html',
   'D:\\\u200f\u200fPostal Box Management\\app\\templates\\auth\\users.html',
   'DATA'),
  ('app\\templates\\backup\\index.html',
   'D:\\\u200f\u200fPostal Box Management\\app\\templates\\backup\\index.html',
   'DATA'),
  ('app\\templates\\backup\\monitor.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\backup\\monitor.html',
   'DATA'),
  ('app\\templates\\backup\\settings.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\backup\\settings.html',
   'DATA'),
  ('app\\templates\\base.html',
   'D:\\\u200f\u200fPostal Box Management\\app\\templates\\base.html',
   'DATA'),
  ('app\\templates\\dashboard.html',
   'D:\\\u200f\u200fPostal Box Management\\app\\templates\\dashboard.html',
   'DATA'),
  ('app\\templates\\notifications\\index.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\notifications\\index.html',
   'DATA'),
  ('app\\templates\\notifications\\settings.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\notifications\\settings.html',
   'DATA'),
  ('app\\templates\\reports\\expired.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\reports\\expired.html',
   'DATA'),
  ('app\\templates\\reports\\expiring.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\reports\\expiring.html',
   'DATA'),
  ('app\\templates\\reports\\financial.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\reports\\financial.html',
   'DATA'),
  ('app\\templates\\reports\\index.html',
   'D:\\\u200f\u200fPostal Box Management\\app\\templates\\reports\\index.html',
   'DATA'),
  ('app\\templates\\subscribers\\add.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\subscribers\\add.html',
   'DATA'),
  ('app\\templates\\subscribers\\edit.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\subscribers\\edit.html',
   'DATA'),
  ('app\\templates\\subscribers\\index.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\subscribers\\index.html',
   'DATA'),
  ('app\\templates\\subscribers\\view.html',
   'D:\\\u200f\u200fPostal Box '
   'Management\\app\\templates\\subscribers\\view.html',
   'DATA'),
  ('config.py', 'D:\\\u200f\u200fPostal Box Management\\config.py', 'DATA'),
  ('icon.ico', 'D:\\\u200f\u200fPostal Box Management\\icon.ico', 'DATA'),
  ('instance\\postal_box_management.db',
   'D:\\\u200f\u200fPostal Box Management\\instance\\postal_box_management.db',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzlocal-5.3.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzlocal-5.3.1.dist-info\\INSTALLER',
   'DATA'),
  ('tzdata-2025.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata-2025.2.dist-info\\RECORD',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzlocal-5.3.1.dist-info\\METADATA',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzlocal-5.3.1.dist-info\\top_level.txt',
   'DATA'),
  ('tzdata-2025.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata-2025.2.dist-info\\INSTALLER',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzlocal-5.3.1.dist-info\\RECORD',
   'DATA'),
  ('tzdata-2025.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata-2025.2.dist-info\\METADATA',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\APScheduler-3.11.0.dist-info\\WHEEL',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\APScheduler-3.11.0.dist-info\\top_level.txt',
   'DATA'),
  ('tzdata-2025.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata-2025.2.dist-info\\top_level.txt',
   'DATA'),
  ('tzdata-2025.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata-2025.2.dist-info\\WHEEL',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\APScheduler-3.11.0.dist-info\\REQUESTED',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzlocal-5.3.1.dist-info\\WHEEL',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\APScheduler-3.11.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('tzdata-2025.2.dist-info\\licenses\\licenses\\LICENSE_APACHE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata-2025.2.dist-info\\licenses\\licenses\\LICENSE_APACHE',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\APScheduler-3.11.0.dist-info\\entry_points.txt',
   'DATA'),
  ('tzdata-2025.2.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata-2025.2.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\APScheduler-3.11.0.dist-info\\INSTALLER',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\APScheduler-3.11.0.dist-info\\METADATA',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\APScheduler-3.11.0.dist-info\\RECORD',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coyhaique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('tzdata\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\leapseconds',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('tzdata\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Merida',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Belem',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('tzdata\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('tzdata\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\CET',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\North',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('tzdata\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('tzdata\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\zone.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('tzdata\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('tzdata\\zones',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zones',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Panama',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\New_York',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('tzdata\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Hongkong',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('tzdata\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\PRC',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('tzdata\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Lima',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\CST6CDT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('tzdata\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Poland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Regina',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('tzdata\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\GB',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Thule',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Iceland',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Factory',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('tzdata\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Creston',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('tzdata\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\EST',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('tzdata\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Turkey',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Israel',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Adak',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\GB-Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Havana',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\EET',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('tzdata\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\PST8PDT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('tzdata\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\HST',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('tzdata\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\London',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('tzdata\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\MET',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Atka',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('tzdata\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Managua',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\ROK',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('tzdata\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\MST',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('tzdata\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Iran',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('tzdata\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Belize',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('tzdata\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\MST7MDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\zonenow.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('tzdata\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Navajo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Recife',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('tzdata\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\W-SU',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\South',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('tzdata\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\ROC',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Denver',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tzdata\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('tzdata\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\WET',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('tzdata\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Libya',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('tzdata\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('tzdata\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Portugal',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('tzdata\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Egypt',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Nome',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('tzdata\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\EST5EDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('tzdata\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Japan',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\NZ',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Boise',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('tzdata\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Cuba',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('zope.interface-7.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.interface-7.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('zope.event-5.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.event-5.0.dist-info\\top_level.txt',
   'DATA'),
  ('cffi-1.17.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi-1.17.1.dist-info\\WHEEL',
   'DATA'),
  ('zope.event-5.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.event-5.0.dist-info\\INSTALLER',
   'DATA'),
  ('zope.interface-7.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.interface-7.2.dist-info\\METADATA',
   'DATA'),
  ('gevent-25.5.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent-25.5.1.dist-info\\top_level.txt',
   'DATA'),
  ('pycparser-2.22.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser-2.22.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\INSTALLER',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\licenses\\LICENSE.PSF',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\greenlet-3.2.2.dist-info\\licenses\\LICENSE.PSF',
   'DATA'),
  ('zope.interface-7.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.interface-7.2.dist-info\\RECORD',
   'DATA'),
  ('cffi-1.17.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi-1.17.1.dist-info\\entry_points.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent-25.5.1.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('zope.interface-7.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.interface-7.2.dist-info\\WHEEL',
   'DATA'),
  ('zope.interface-7.2.dist-info\\namespace_packages.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.interface-7.2.dist-info\\namespace_packages.txt',
   'DATA'),
  ('cffi-1.17.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi-1.17.1.dist-info\\top_level.txt',
   'DATA'),
  ('pycparser-2.22.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser-2.22.dist-info\\INSTALLER',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\greenlet-3.2.2.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\LICENSE',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\LICENSE',
   'DATA'),
  ('cffi-1.17.1.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi-1.17.1.dist-info\\LICENSE',
   'DATA'),
  ('zope.event-5.0.dist-info\\namespace_packages.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.event-5.0.dist-info\\namespace_packages.txt',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\METADATA',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\METADATA',
   'DATA'),
  ('zope.event-5.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.event-5.0.dist-info\\METADATA',
   'DATA'),
  ('pycparser-2.22.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser-2.22.dist-info\\RECORD',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\RECORD',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\REQUESTED',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\REQUESTED',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\greenlet-3.2.2.dist-info\\WHEEL',
   'DATA'),
  ('zope.interface-7.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.interface-7.2.dist-info\\INSTALLER',
   'DATA'),
  ('gevent-25.5.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent-25.5.1.dist-info\\RECORD',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\entry_points.txt',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\entry_points.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\greenlet-3.2.2.dist-info\\top_level.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent-25.5.1.dist-info\\INSTALLER',
   'DATA'),
  ('zope.event-5.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.event-5.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('cffi-1.17.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi-1.17.1.dist-info\\RECORD',
   'DATA'),
  ('cffi-1.17.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi-1.17.1.dist-info\\INSTALLER',
   'DATA'),
  ('cffi-1.17.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cffi-1.17.1.dist-info\\METADATA',
   'DATA'),
  ('pycparser-2.22.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser-2.22.dist-info\\LICENSE',
   'DATA'),
  ('gevent-25.5.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent-25.5.1.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\WHEEL',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\WHEEL',
   'DATA'),
  ('pycparser-2.22.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser-2.22.dist-info\\top_level.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\greenlet-3.2.2.dist-info\\METADATA',
   'DATA'),
  ('zope.event-5.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.event-5.0.dist-info\\RECORD',
   'DATA'),
  ('gevent-25.5.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent-25.5.1.dist-info\\METADATA',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\greenlet-3.2.2.dist-info\\RECORD',
   'DATA'),
  ('gevent-25.5.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\gevent-25.5.1.dist-info\\entry_points.txt',
   'DATA'),
  ('zope.interface-7.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.interface-7.2.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\top_level.txt',
   'C:\\Program '
   'Files\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\top_level.txt',
   'DATA'),
  ('zope.event-5.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\zope.event-5.0.dist-info\\WHEEL',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\greenlet-3.2.2.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('pycparser-2.22.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pycparser-2.22.dist-info\\METADATA',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography-41.0.4.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography-41.0.4.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography-41.0.4.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\LICENSE.BSD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography-41.0.4.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography-41.0.4.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography-41.0.4.dist-info\\RECORD',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography-41.0.4.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography-41.0.4.dist-info\\METADATA',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography-41.0.4.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Program Files\\Python311\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'C:\\Program Files\\Python311\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tk_data\\console.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'C:\\Program Files\\Python311\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tk_data\\tclIndex',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tk_data\\text.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'C:\\Program Files\\Python311\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Program Files\\Python311\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Program '
   'Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tk_data\\button.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'C:\\Program Files\\Python311\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:\\Program Files\\Python311\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dnspython-2.7.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('flask-2.3.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug-2.3.7.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dnspython-2.7.0.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug-2.3.7.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug-2.3.7.dist-info\\REQUESTED',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug-2.3.7.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dnspython-2.7.0.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-2.3.3.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'DATA'),
  ('flask-2.3.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\flask-2.3.3.dist-info\\LICENSE.rst',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\werkzeug-2.3.7.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dnspython-2.7.0.dist-info\\METADATA',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\dnspython-2.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'D:\\\u200f\u200fPostal Box '
   'Management\\build\\PostalBoxManager\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1752728668,
 [('runw.exe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'C:\\Program Files\\Python311\\python311.dll')
