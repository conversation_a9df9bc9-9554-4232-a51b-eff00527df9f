{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة الصناديق البريدية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users-cog me-2"></i>إدارة المستخدمين</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">إدارة المستخدمين</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
                            </a>
                        </div>
                        <div class="col-md-6">
                            <form method="GET" class="d-flex">
                                <input type="text" class="form-control me-2" name="search" 
                                       value="{{ search }}" placeholder="البحث عن مستخدم...">
                                <button type="submit" class="btn btn-outline-secondary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة المستخدمين -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>قائمة المستخدمين ({{ users.total }} مستخدم)
                    </h5>
                </div>
                <div class="card-body">
                    {% if users.items %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>الاسم الكامل</th>
                                        <th>اسم المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الصلاحيات</th>
                                        <th>الحالة</th>
                                        <th>آخر دخول</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user in users.items %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-user-circle fa-2x text-primary"></i>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ user.full_name }}</strong>
                                                    {% if user.id == current_user.id %}
                                                        <span class="badge bg-info ms-1">أنت</span>
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td>
                                                <code>{{ user.username }}</code>
                                            </td>
                                            <td>
                                                <a href="mailto:{{ user.email }}" class="text-decoration-none">
                                                    {{ user.email }}
                                                </a>
                                            </td>
                                            <td>
                                                {% if user.is_admin %}
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="fas fa-crown me-1"></i>مدير
                                                    </span>
                                                {% else %}
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-user me-1"></i>مستخدم
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if user.is_active %}
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check-circle me-1"></i>نشط
                                                    </span>
                                                {% else %}
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-times-circle me-1"></i>معطل
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if user.last_login %}
                                                    <small>{{ user.last_login.strftime('%Y-%m-%d %H:%M') }}</small>
                                                {% else %}
                                                    <small class="text-muted">لم يسجل دخول</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ url_for('auth.edit_user', user_id=user.id) }}" 
                                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                                            onclick="resetPassword({{ user.id }}, '{{ user.username }}')" title="إعادة تعيين كلمة المرور">
                                                        <i class="fas fa-key"></i>
                                                    </button>
                                                    {% if user.id != current_user.id %}
                                                        <button type="button" class="btn btn-sm btn-outline-{{ 'danger' if user.is_active else 'success' }}" 
                                                                onclick="toggleUserStatus({{ user.id }}, '{{ user.username }}', {{ user.is_active|lower }})" 
                                                                title="{{ 'إلغاء التفعيل' if user.is_active else 'تفعيل' }}">
                                                            <i class="fas fa-{{ 'ban' if user.is_active else 'check' }}"></i>
                                                        </button>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if users.pages > 1 %}
                            <nav aria-label="صفحات المستخدمين">
                                <ul class="pagination justify-content-center">
                                    {% if users.has_prev %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('auth.users', page=users.prev_num, search=search) }}">السابق</a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for page_num in users.iter_pages() %}
                                        {% if page_num %}
                                            {% if page_num != users.page %}
                                                <li class="page-item">
                                                    <a class="page-link" href="{{ url_for('auth.users', page=page_num, search=search) }}">{{ page_num }}</a>
                                                </li>
                                            {% else %}
                                                <li class="page-item active">
                                                    <span class="page-link">{{ page_num }}</span>
                                                </li>
                                            {% endif %}
                                        {% else %}
                                            <li class="page-item disabled">
                                                <span class="page-link">...</span>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if users.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('auth.users', page=users.next_num, search=search) }}">التالي</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مستخدمين</h5>
                            {% if search %}
                                <p class="text-muted">لم يتم العثور على مستخدمين يطابقون البحث "{{ search }}"</p>
                                <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>إلغاء البحث
                                </a>
                            {% else %}
                                <p class="text-muted">قم بإضافة أول مستخدم للنظام</p>
                                <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
                                    <i class="fas fa-user-plus me-2"></i>إضافة مستخدم
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإعادة تعيين كلمة المرور -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعادة تعيين كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="resetPasswordForm">
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="newPassword" required minlength="6">
                        <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="submitResetPassword()">
                    <i class="fas fa-key me-2"></i>إعادة تعيين
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentUserId = null;
let currentUsername = null;

function resetPassword(userId, username) {
    currentUserId = userId;
    currentUsername = username;
    
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmPassword').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

function submitResetPassword() {
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (newPassword !== confirmPassword) {
        showAlert('danger', 'كلمات المرور غير متطابقة');
        return;
    }
    
    if (newPassword.length < 6) {
        showAlert('danger', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        return;
    }
    
    fetch(`/auth/users/${currentUserId}/reset-password`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            new_password: newPassword
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        showAlert('danger', 'حدث خطأ في الاتصال');
    });
}

function toggleUserStatus(userId, username, isActive) {
    const action = isActive ? 'إلغاء تفعيل' : 'تفعيل';
    
    if (!confirm(`هل أنت متأكد من ${action} المستخدم "${username}"؟`)) {
        return;
    }
    
    fetch(`/auth/users/${userId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        showAlert('danger', 'حدث خطأ في الاتصال');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
